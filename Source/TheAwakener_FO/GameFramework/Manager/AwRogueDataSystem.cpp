// Fill out your copyright notice in the Description page of Project Settings.


#include "AwRogueDataSystem.h"

#include "Kismet/KismetMathLibrary.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GamePlay/GameSave/AwRogueSaveGame.h"
#include "TheAwakener_FO/GamePlay/Roguelike/BattleStyle/RogueBattleStyleSubSystem.h"
#include "TheAwakener_FO/GamePlay/Roguelike/BattleUpgrade/RogueBattleUpgradeSubSystem.h"
#include "TheAwakener_FO/GamePlay/Thing/ItemInstanceSubsystem.h"

void UAwRogueDataSystem::InitSubSystem()
{
}

void UAwRogueDataSystem::SaveData()
{
	CheckRogueSaveGame();

	const AAwCharacter* TempCharacter = UGameplayFuncLib::GetLocalAwPlayerCharacter(0);
	if(TempCharacter)
	{
		SetHP_CurBattle(TempCharacter->CharacterObj.CurrentRes.HP);
		SetAP_CurBattle(TempCharacter->CharacterObj.CurrentRes.AP);
	}


	for (auto PC : UGameplayFuncLib::GetAllLocalAwPlayerControllers())
	{
		if (!PC)continue;
		auto PS = PC->GetPlayerState<AAwPlayerState>();
		int PcId = PC->GetLocalPCIndex();
		auto AwakeSkill = RogueSaveGame->RogueDataInfos[RogueDataIndex].AwakeSkillId;
		if (PS&&PS->CurAwakeSkill)
		{
			while (AwakeSkill.Num()<=PcId)
				AwakeSkill.Add("Earthquake");
			AwakeSkill[PcId] =PS->CurAwakeSkill->Id;
		}
	}
	// else if(UGameplayFuncLib::GetLocalAwPlayerState())
	// {
	// 	//RogueSaveGame->RogueDataInfos[RogueDataIndex].AwakeSkillId = "";
	// }

	TSet<FString>UniqueIds;
	for (auto PC : UGameplayFuncLib::GetAllLocalAwPlayerControllers())
	{
		if (!PC)continue;
		auto PS = PC->GetPlayerState<AAwPlayerState>();
		if(PS && PS->AfterLoadSaveData)
		{
			for(FString id :PS->UnLockAwakeSkills)
			{
				UniqueIds.Add(id);
			}
		}
	}
	TArray<FString> Res = UniqueIds.Array();
	RogueSaveGame->RogueDataInfos[RogueDataIndex].UnLockAwakeSkills = Res;
	if (RogueSaveGame->RogueDataInfos[RogueDataIndex].UnLockAwakeSkills.IsEmpty())
	{
		UE_LOG(LogTemp, Warning, TEXT("TSet Save Empty UnlockSkills"));
	}
	
	
	//保存难度挑战
	if (UGameplayFuncLib::GetAwGameInstance())
	{
		RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.Rogue_ChallengeList = UGameplayFuncLib::GetAwGameInstance()->Rogue_ChallengeList;
	}

	//清空原存档数据并将最新的实时数据最为新存档数据
	ClearRelicHasGet_CurBattle();
	GetGameInstance()->GetSubsystem<UAwRelicSubSystem>()->SaveData();
	SetCurItem_CurBattle(FAwRogueItemInfo());
	SetCurItem_CurBattle(FAwRogueItemInfo(),false);
	SetHealingPotion_CurBattle(FAwRogueItemInfo());
	GetGameInstance()->GetSubsystem<UAwRogueItemSubSystem>()->SaveData();
	ClearUnlockTalent();
	GetGameInstance()->GetSubsystem<UAwRogueTalentSubSystem>()->SaveData();

	if (RogueSaveGame->RogueDataInfos[RogueDataIndex].UnLockAwakeSkills.IsEmpty())
	{
		UE_LOG(LogTemp, Warning, TEXT("Save Empty UnlockSkills"));
	}


	
	UGameplayStatics::SaveGameToSlot(RogueSaveGame, Aw_Rogue_GameSaveData, 0);
}

bool UAwRogueDataSystem::DeleteSaveData(int Index)
{
	if (RogueSaveGame->RogueDataInfos.Num() > Index)
	{
		RogueSaveGame->RogueDataInfos.RemoveAt(Index);
		UGameplayStatics::SaveGameToSlot(RogueSaveGame, Aw_Rogue_GameSaveData, 0);
		return true;
	}
	return false;
}

void UAwRogueDataSystem::PostLoadSaveDataInHall(int PlayerIndex)
{
	if (PlayerIndex==0){
		//血药，法器 还原
		GetGameInstance()->GetSubsystem<UAwRogueItemSubSystem>()->ResetAllItemToMeta();

		// 血药，法器
		GetGameInstance()->GetSubsystem<UAwRogueItemSubSystem>()->ReApplySaveDataModify();
	}
	FRogueDataInfo DataInfo = RogueSaveGame->RogueDataInfos[RogueDataIndex];
	AAwPlayerController* PC = UGameplayFuncLib::GetLocalAwPlayerController(PlayerIndex);

	// 遗物
	GetGameInstance()->GetSubsystem<UAwRelicSubSystem>()->ClearRelicEffect();
	GetGameInstance()->GetSubsystem<UAwRelicSubSystem>()->ReGetRelic();
	// 暗黑like道具
	GetGameInstance()->GetSubsystem<UItemInstanceSubsystem>()->ReGetItemInstanceEquipped();
	// 天赋 效果清空
	GetGameInstance()->GetSubsystem<UAwRogueTalentSubSystem>()->ClearTalentEffect(PC);
	// 天赋
	GetGameInstance()->GetSubsystem<UAwRogueTalentSubSystem>()->ReGetTalentEffect(PC);
	GetGameInstance()->GetSubsystem<UAwRogueItemSubSystem>()->SetCurRogueItem("",1,true);
	GetGameInstance()->GetSubsystem<UAwRogueItemSubSystem>()->SetCurRogueItem("",1,false);

	
	if(UGameplayFuncLib::GetLocalAwPlayerState(PlayerIndex))
	{
		// 觉醒技能解锁
		for (const FString SkillId : DataInfo.UnLockAwakeSkills) 
			UGameplayFuncLib::GetLocalAwPlayerState(PlayerIndex)->UnlockPlayerAwakeSkill(SkillId);

		UGameplayFuncLib::GetLocalAwPlayerState(PlayerIndex)->AfterLoadSaveData= true;
		// 觉醒技能
		UGameplayFuncLib::GetLocalAwPlayerState(PlayerIndex)->SetCurAwakeSkill(GetAwakeSkillId(DataInfo, PlayerIndex));
	}
	
	// 设置角色预览技能
	URogueBattleUpgradeSubSystem* BattleUpgradeSubSystem = GetGameInstance()->GetSubsystem<URogueBattleUpgradeSubSystem>();
	
	BattleUpgradeSubSystem->OnClearBattle();
	FString classID = GetCurPawnClassId(PlayerIndex);
	if(DataInfo.PreviewAbilityInfos.Contains(classID))
		BattleUpgradeSubSystem->SetAbilityInfos(PlayerIndex,GetPreviewAbilityLevelInfos(classID), false);
	else
	{
		BattleUpgradeSubSystem->SettPreviewAbilityInfos(PlayerIndex,GetDefaultAbilityLevelInfos(classID), false);
		BattleUpgradeSubSystem->SetAbilityInfos(PlayerIndex,GetDefaultAbilityLevelInfos(classID), false);
	}
}

void UAwRogueDataSystem::PostLoadSaveDataInBattle(int PlayerIndex)
{
	const FRogueDataInfo DataInfo = RogueSaveGame->RogueDataInfos[RogueDataIndex];
	AAwPlayerController* PC = UGameplayFuncLib::GetLocalAwPlayerController(PlayerIndex);
	AAwPlayerState* PS = PC->GetPlayerState<AAwPlayerState>();
	if (PlayerIndex==0)
	{
		//血药，法器 还原
		GetGameInstance()->GetSubsystem<UAwRogueItemSubSystem>()->ResetAllItemToMeta();
		// 血药，法器
		GetGameInstance()->GetSubsystem<UAwRogueItemSubSystem>()->ReApplySaveDataModify();
	}
	//遗物 效果清空
	GetGameInstance()->GetSubsystem<UAwRelicSubSystem>()->ClearRelicEffect();
	// 天赋 效果清空
	GetGameInstance()->GetSubsystem<UAwRogueTalentSubSystem>()->ClearTalentEffect(PC);
	// 天赋
	GetGameInstance()->GetSubsystem<UAwRogueTalentSubSystem>()->ReGetTalentEffect(PC);
	// 遗物
	GetGameInstance()->GetSubsystem<UAwRelicSubSystem>()->ReGetRelic();
	// 暗黑like道具
	GetGameInstance()->GetSubsystem<UItemInstanceSubsystem>()->ReGetItemInstanceEquipped();
	
	if(PS)
	{
		// 觉醒技能解锁
		for (const FString SkillId : DataInfo.UnLockAwakeSkills) 
			PS->UnlockPlayerAwakeSkill(SkillId);

		PS->AfterLoadSaveData= true;
		// 觉醒技能
		PS->SetCurAwakeSkill(GetAwakeSkillId(DataInfo,PlayerIndex));
	}
	
	
	const FBattleDataInfo BattleDataInfo = DataInfo.CurBattleDataInfo;

	FString CurrentStyleId = DataInfo.BattleStyle[BattleDataInfo.PawnClassId];
	// 动作强化
	// if (BattleDataInfo.BattleStyleUpgradeInfo.Contains(CurrentStyleId)) 
	// 	GetGameInstance()->GetSubsystem<URogueBattleStyleSubSystem>()->ReUpgradeInBattle(
	// 		BattleDataInfo.BattleStyleUpgradeInfo[CurrentStyleId].Ids);
	// 战斗强化
	// UKismetSystemLibrary::PrintString(this, "---------- ---------- ----------");
	// UKismetSystemLibrary::PrintString(this, "CurPawnClassId:" + GetCurPawnClassId());
	// UKismetSystemLibrary::PrintString(this, "  NormalAttack:" + GetCurBattleAbilityIds().NormalAttack);
	// UKismetSystemLibrary::PrintString(this, "       Ground1:" + GetCurBattleAbilityIds().Ground1);
	// UKismetSystemLibrary::PrintString(this, "       Ground2:" + GetCurBattleAbilityIds().Ground2);
	// UKismetSystemLibrary::PrintString(this, "          Air1:" + GetCurBattleAbilityIds().Air1);
	// UKismetSystemLibrary::PrintString(this, "          Air2:" + GetCurBattleAbilityIds().Air2);
	// UKismetSystemLibrary::PrintString(this, "----------");
	// UKismetSystemLibrary::PrintString(this, "  NormalAttack:" + GetPreviewBattleAbilityIds(GetCurPawnClassId()).NormalAttack);
	// UKismetSystemLibrary::PrintString(this, "       Ground1:" + GetPreviewBattleAbilityIds(GetCurPawnClassId()).Ground1);
	// UKismetSystemLibrary::PrintString(this, "       Ground2:" + GetPreviewBattleAbilityIds(GetCurPawnClassId()).Ground2);
	// UKismetSystemLibrary::PrintString(this, "          Air1:" + GetPreviewBattleAbilityIds(GetCurPawnClassId()).Air1);
	// UKismetSystemLibrary::PrintString(this, "          Air2:" + GetPreviewBattleAbilityIds(GetCurPawnClassId()).Air2);
	// UKismetSystemLibrary::PrintString(this, "---------- ----------");
	
	URogueBattleUpgradeSubSystem* BattleUpgradeSubSystem = GetGameInstance()->GetSubsystem<URogueBattleUpgradeSubSystem>();
	AAwCharacter* PlayerCharacter = UGameplayFuncLib::GetLocalAwPlayerCharacter(PlayerIndex);
	BattleUpgradeSubSystem->SetAbilityInfos(PlayerIndex,GetCurAbilityLevelInfos(PlayerCharacter->PlayerClassId), false);
	//设置角色信息  血量，觉醒值，重新计算buff一些实时检测属性
	if(PlayerCharacter)
	{
		for (int i = 0; i < PlayerCharacter->CharacterObj.Buff.Num(); i++)
		{
			for (int j = 0; j < PlayerCharacter->CharacterObj.Buff[i].Model.AfterRogueLoadSaveData.Num(); j++)
			{
				UFunction* Func = UCallFuncLib::JsonFuncToUFunc(PlayerCharacter->CharacterObj.Buff[i].Model.AfterRogueLoadSaveData[j]);
				if (IsValid(Func) == false) continue;
			
				struct {
					FBuffObj BuffObj;
					int32 Ticked;
					TArray<FString> Params;
				
					FBuffRunResult Result;
				} FuncParam;
			
				FuncParam.BuffObj = PlayerCharacter->CharacterObj.Buff[i];
				FuncParam.Ticked = 0;
				FuncParam.Params = PlayerCharacter->CharacterObj.Buff[i].Model.AfterRogueLoadSaveData[j].Params;
				PlayerCharacter->ProcessEvent(Func, &FuncParam);
				PlayerCharacter->CharacterObj.Buff[i] = FuncParam.Result.BuffObj;
			}
		}
		PlayerCharacter->AttrRecheck();
		PlayerCharacter->CharacterObj.CurrentRes.HP = GetHP_CurBattle();
		PlayerCharacter->CharacterObj.CurrentRes.AP = GetAP_CurBattle();
	}
}

void UAwRogueDataSystem::StartBattle()
{
	CheckRogueSaveGame();
	ClearBattle();
	//UKismetSystemLibrary::PrintString(this,"StartBattle");
	SetCurBattleDataIsActive(true);
	for (auto PC : UGameplayFuncLib::GetAllLocalAwPlayerControllers())
	{
		if (!PC)continue;
		GetGameInstance()->GetSubsystem<UAwRogueTalentSubSystem>()->ReGetTalentEffect(PC);
		GetGameInstance()->GetSubsystem<UAwRogueTalentSubSystem>()->TalentBuffOnGameStart(PC);
	}
	// 战斗风格默认强化
	GetGameInstance()->GetSubsystem<URogueBattleStyleSubSystem>()->UpgradeDefault();
	//GetGameInstance()->GetSubsystem<UAwRelicSubSystem>()->GivePlayerInitialRelic();
	for (auto PC : UGameplayFuncLib::GetAllLocalAwPlayerControllers())
	{
		if (!PC)continue;
		PC->CurCharacter->FullyRestore();
	}
}

void UAwRogueDataSystem::ClearBattle()
{
	CheckRogueSaveGame();

	const FRogueDataInfo DataInfo = RogueSaveGame->RogueDataInfos[RogueDataIndex];
	SetCurBattleDataIsActive(false);
	SetPawnClassId_CurBattle(DataInfo.CurPawnClassId,0);
	SetPawnClassId_CurBattle(DataInfo.CurPawnClassIdP2,1);
	SetCurrencyCount(0, "Rogue_Coin", true);
	SetCurrencyCount(0, "Rogue_Soul", true);
	SetCurrencyCount(0, "Rogue_Key", true);
	SetCurrencyCount(0, "Rogue_Shard", true);
	ClearRelicHasGet_CurBattle();
	for (auto PC : UGameplayFuncLib::GetAllLocalAwPlayerControllers())
	{
		if (!PC)continue;
		GetGameInstance()->GetSubsystem<UAwRelicSubSystem>()->ClearRelic();
		GetGameInstance()->GetSubsystem<UAwRogueTalentSubSystem>()->ClearTalentEffect(PC);
	}
	GetGameInstance()->GetSubsystem<URogueBattleUpgradeSubSystem>()->OnClearBattle();
	
	UAwRogueItemSubSystem* ItemSubSystem = GetGameInstance()->GetSubsystem<UAwRogueItemSubSystem>();
	ItemSubSystem->ResetAllItemToMeta();
	SetHealingPotion_CurBattle(ItemSubSystem->CurHealingPotion);

	SetRevivedChance_CurBattle(0);
	SetRevivedChanceHasUse_CurBattle(0);

	SetHasSelectStartBless(false);
	SetStep_CurBattle(0);
	//RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.UsedLeveList.Empty();
	SetLevelName_CurBattle("");
	SetRoomName_CurBattle("");
	SetRoomReward_CurBattle(TArray<ERogueRoomReward>());
	RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.CurBattleTime = 0;
	RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.IsClearThisRoom = false;
	RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RogueRoomReward = FRogueRoomReward();
	RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.BattleStyleUpgradeInfo.Empty();
	RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.NextRoomList.Empty();
	// if (UGameplayFuncLib::GetMyAwPlayerState())
	// 	UGameplayFuncLib::GetMyAwPlayerState()->SetCurAwakeSkill(DataInfo.AwakeSkillId);

	RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.BattleStyleUpgradeReward.Point = 0;
	RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.BattleStyleUpgradeReward.MaxPoint = 1;
	RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.BattleStyleUpgradeReward.RandomUpgradeIds.Empty();
	
	RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.TotalKilledNormalNum = 0;
	RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.TotalKilledEliteNum = 0;
	RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.TotalKilledBossNum = 0;
	RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.ChallengeTimes = 0;
	RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.Rogue_ChallengeList.Empty();
}

void UAwRogueDataSystem::CheckRogueSaveGame()
{
	if (RogueSaveGame == nullptr)
		RogueSaveGame = Cast<UAwRogueSaveGame>(UGameplayStatics::CreateSaveGameObject(UAwRogueSaveGame::StaticClass()));
	
	if (RogueSaveGame->RogueDataInfos.Num() <= 0)
		RogueSaveGame->RogueDataInfos.Add(FRogueDataInfo());
	//updage RogueSave content
	//get id from roguepawns.id to characterKeys
	UAwDataManager* DataManager = UGameplayFuncLib::GetDataManager();
	TArray<FString> characterKeys;
	for (const auto& RoguePawn : DataManager->RoleCreation.RoguePawns)
	{
		characterKeys.Add(RoguePawn.Id);
	}

	for (FString key : characterKeys)
	{
		if (!RogueSaveGame->RogueDataInfos[RogueDataIndex].BattleRecord.ClassRecords.Contains(key))
		{
			RogueSaveGame->RogueDataInfos[RogueDataIndex].BattleRecord.ClassRecords.Add(key, FRogueClassRecord());
			// UE_LOG(LogTemp, Warning, TEXT("%s not found in ClassRecords"), *key);
		}
	}
	for (int i = 0; i < RogueSaveGame->RogueDataInfos.Num(); ++i)
	{
		// UAwDataManager* DataManager = UGameplayFuncLib::GetDataManager();
		for (FString PawnClassId : DataManager->GetAllRoguePawnsClassId())
		{
			if (!RogueSaveGame->RogueDataInfos[i].BattleStyle.Contains(PawnClassId))
				RogueSaveGame->RogueDataInfos[i].BattleStyle.Add(
					PawnClassId,
					DataManager->GetRolePawnByClassId(PawnClassId).DefaultBattleStyle);
		}
	}
}

UAwRogueSaveGame* UAwRogueDataSystem::LoadSaveData(bool& HaveSaveFile)
{
	UAwRogueSaveGame* OldSaveData = Cast<UAwRogueSaveGame>(UGameplayStatics::LoadGameFromSlot("AwRogueGameSaveData", 0));
	
	RogueSaveGame = Cast<UAwRogueSaveGame>(UGameplayStatics::LoadGameFromSlot(Aw_Rogue_GameSaveData, 0));
	HaveSaveFile = RogueSaveGame != nullptr;
	CheckRogueSaveGame();
	
	ResetOldSaveGame(OldSaveData,RogueSaveGame);
		
	const FRogueDataInfo DataInfo = RogueSaveGame->RogueDataInfos[RogueDataIndex];
	//以下都应该只读取数据 并不直接生效数据对应效果  效果应于PostLoad里调用
	if (UGameplayFuncLib::GetAwGameInstance())
	{
		UGameplayFuncLib::GetAwGameInstance()->Rogue_ChallengeList = DataInfo.CurBattleDataInfo.Rogue_ChallengeList;
	}
	// 天赋
	GetGameInstance()->GetSubsystem<UAwRogueTalentSubSystem>()->LoadSaveData();
	// 遗物
	GetGameInstance()->GetSubsystem<UAwRelicSubSystem>()->LoadSaveData();
	// 血药，法器
	GetGameInstance()->GetSubsystem<UAwRogueItemSubSystem>()->LoadSaveData();
	return RogueSaveGame;
}

FRogueDataInfo& UAwRogueDataSystem::GetCurRogueData(bool& hasData)
{
	return GetCurRogueDataByIndex(hasData);
}

FRogueDataInfo& UAwRogueDataSystem::GetCurRogueDataByIndex(bool& hasData)
{
	FRogueDataInfo Res;
	CheckRogueSaveGame();
	
	if (RogueSaveGame && RogueSaveGame->RogueDataInfos.Num() > RogueDataIndex)
	{
		hasData = true;
		return RogueSaveGame->RogueDataInfos[RogueDataIndex];
	}
	
	hasData = false;
	return RogueSaveGame->RogueDataInfos[RogueDataIndex];
}

FSettingDataInfo UAwRogueDataSystem::GetSettingData()
{
	CheckRogueSaveGame();
	return RogueSaveGame->SettingDataInfo;
}

bool UAwRogueDataSystem::HasCurRogueData() const
{
	if (RogueSaveGame && RogueSaveGame->RogueDataInfos.Num() > RogueDataIndex)
		return true;
	return false;
}

bool UAwRogueDataSystem::GetIsNew() const
{
	if (HasCurRogueData())
		return RogueSaveGame->RogueDataInfos[RogueDataIndex].IsNew;
	return false;
}

void UAwRogueDataSystem::SetNotNew() const
{
	if (HasCurRogueData())
		RogueSaveGame->RogueDataInfos[RogueDataIndex].IsNew = false;
}

FString UAwRogueDataSystem::GetCurPawnClassId(int index) const
{
	// UE_LOG(LogTemp,Log,TEXT("UAwRogueDataSystem::GetCurPawnClassId RogueData:%s PlayerData:%s"),*RogueSaveGame->RogueDataInfos[RogueDataIndex].CurPawnClassId,*UGameplayFuncLib::GetAwGameState()->PlayerCharacters[0]->PlayerClassId);
	
	FString res;
	if (UGameplayFuncLib::GetLocalAwPlayerCharacter(index) != NULL)
		res = UGameplayFuncLib::GetLocalAwPlayerCharacter(index)->PlayerClassId;
	if (res.IsEmpty())
	{
		if (HasCurRogueData())
		{
			if (index !=1)
			{
				return RogueSaveGame->RogueDataInfos[RogueDataIndex].CurPawnClassId;
			}
			else
			{
				return RogueSaveGame->RogueDataInfos[RogueDataIndex].CurPawnClassIdP2;
			}
		}
	}
	
	return res;
}

void UAwRogueDataSystem::SetCurPawnClassId(FString PawnId,int PlayerIndex)
{
	if (HasCurRogueData())
	{
		if (PlayerIndex!=1)
		{
			RogueSaveGame->RogueDataInfos[RogueDataIndex].CurPawnClassId = PawnId;
		}
		else
		{
			RogueSaveGame->RogueDataInfos[RogueDataIndex].CurPawnClassIdP2 = PawnId;
		}
	}
}

int UAwRogueDataSystem::GetCurSkinId(FString ClassId) const
{
	if (HasCurRogueData() && RogueSaveGame->RogueDataInfos[RogueDataIndex].SkinId.Contains(ClassId))
		return RogueSaveGame->RogueDataInfos[RogueDataIndex].SkinId[ClassId];
	return 0;
}

void UAwRogueDataSystem::SetCurSkinId(int SkinId, FString ClassId)
{
	if (HasCurRogueData())
	{
		if (RogueSaveGame->RogueDataInfos[RogueDataIndex].SkinId.Contains(ClassId))
		{
			RogueSaveGame->RogueDataInfos[RogueDataIndex].SkinId[ClassId] = SkinId;
		}
		else
		{
			RogueSaveGame->RogueDataInfos[RogueDataIndex].SkinId.Add(ClassId, SkinId);
		}
	}
}

TMap<FString, int> UAwRogueDataSystem::GetAllSwitch() const
{
	if (HasCurRogueData())
		return RogueSaveGame->RogueDataInfos[RogueDataIndex].Switch;
	return TMap<FString, int>();
}

int UAwRogueDataSystem::GetSwitch(FString Key) const
{
	if (HasCurRogueData())
		if (RogueSaveGame->RogueDataInfos[RogueDataIndex].Switch.Contains(Key))
			return RogueSaveGame->RogueDataInfos[RogueDataIndex].Switch[Key];

	return 0;
}

void UAwRogueDataSystem::SetSwitch(FString Key, int Value)
{
	if (HasCurRogueData())
		RogueSaveGame->RogueDataInfos[RogueDataIndex].Switch.Add(Key, Value);	
}

void UAwRogueDataSystem::RemoveSwitch(FString Key)
{
	if (HasCurRogueData())
		if (RogueSaveGame->RogueDataInfos[RogueDataIndex].Switch.Contains(Key))
			RogueSaveGame->RogueDataInfos[RogueDataIndex].Switch.Remove(Key);
}

bool UAwRogueDataSystem::GetIsClearedGame()
{
	if (HasCurRogueData())
		return RogueSaveGame->RogueDataInfos[RogueDataIndex].bGameCleared;
	return false;
}

void UAwRogueDataSystem::SetIsClearedGame(bool bCleared)
{
	if (HasCurRogueData())
		RogueSaveGame->RogueDataInfos[RogueDataIndex].bGameCleared = bCleared;
}

TMap<FString, int> UAwRogueDataSystem::GetAllCurrencyCount(bool bIsInBattle) const
{
	if (HasCurRogueData())
	{
		if (bIsInBattle)
			return RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.Currency;
		return RogueSaveGame->RogueDataInfos[RogueDataIndex].Currency;
	}
	return TMap<FString, int>();
}

int UAwRogueDataSystem::GetCurrencyCount(FString Key, bool bIsInBattle) const
{
	if (HasCurRogueData())
	{
		if (bIsInBattle)
		{
			if (RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.Currency.Contains(Key))
				return RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.Currency[Key];
		}
		else
		{
			if (RogueSaveGame->RogueDataInfos[RogueDataIndex].Currency.Contains(Key))
				return RogueSaveGame->RogueDataInfos[RogueDataIndex].Currency[Key];
		}
	}
	return 0;
}

bool UAwRogueDataSystem::AddCurrencyCount(int Count, FString Key, bool bIsInBattle) const
{
	if (Count == 0)
		return true;
	
	if (HasCurRogueData())
	{
		// 增加
		if (Count > 0)
		{
			// 判断BUFF
			if (AAwCharacter* PlayerCharacter = UGameplayFuncLib::GetLocalAwPlayerCharacter(0))
			{
				if (Key == "Rogue_Coin")
				{
					const int AddStack = PlayerCharacter->GetBuffStackTotal("Rogue_GetCoinUp");
					Count *= 1 + AddStack/100.f;
				}

				if (Key == "Rogue_Key")
				{
					const int AddStack = PlayerCharacter->GetBuffStackTotal("Rogue_GetKeyUp");
					Count += AddStack;
				}

				if (Key == "Rogue_Soul")
				{
					const int AddStack = PlayerCharacter->GetBuffStackTotal("Rogue_GetSoulUp");
					Count *= 1 + AddStack / 100.f;
				}
			}
			
			// 增加战斗数据
			if (bIsInBattle)
			{
				const int NewCount = GetCurrencyCount(Key, true) + Count;
				RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.Currency.Add(Key, NewCount);
			}
			
			if (Key != "Rogue_Coin")
			{
				// 增加战斗数据的同时大厅数据
				const int NewCount = GetCurrencyCount(Key, false) + Count;
				RogueSaveGame->RogueDataInfos[RogueDataIndex].Currency.Add(Key, NewCount);
			}
			
			return true;
		}
		// 减少
		else
		{
			bool CanReduce = true;
			// 减少战斗数据
			if (bIsInBattle)
			{
				const int NewCount = GetCurrencyCount(Key, true) + Count;
				if (NewCount < 0)
					CanReduce &= false;
				else
					RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.Currency.Add(Key, NewCount);
			}

			// 减少战斗数据的同时减少大厅数据
			if (Key != "Rogue_Coin" && CanReduce)
			{
				const int NewCount = GetCurrencyCount(Key, false) + Count;
				if (NewCount < 0)
					CanReduce &= false;
				else
					RogueSaveGame->RogueDataInfos[RogueDataIndex].Currency.Add(Key, NewCount);
			}

			return CanReduce;
		}
	}
	
	return false;
}

void UAwRogueDataSystem::SetCurrencyCount(int Count, FString Key, bool bIsInBattle) const
{
	if (HasCurRogueData())
	{
		// 修改战斗内的数据
		if (bIsInBattle)
			RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.Currency.Add(Key, Count);
		// 修改大厅数据
		else
			RogueSaveGame->RogueDataInfos[RogueDataIndex].Currency.Add(Key, Count);
	}
}

int UAwRogueDataSystem::GetCurrency_Soul(bool bIsInBattle) const
{
	return GetCurrencyCount("Rogue_Soul", bIsInBattle);
}

bool UAwRogueDataSystem::AddCurrency_Soul(int Count, bool bIsInBattle) const
{
	return AddCurrencyCount(Count, "Rogue_Soul", bIsInBattle);
}

int UAwRogueDataSystem::GetCurrency_Key(bool bIsInBattle) const
{
	return GetCurrencyCount("Rogue_Key", bIsInBattle);
}

bool UAwRogueDataSystem::AddCurrency_Key(int Count, bool bIsInBattle) const
{
	return AddCurrencyCount(Count, "Rogue_Key", bIsInBattle);
}

int UAwRogueDataSystem::GetCurrency_Shard(bool bIsInBattle) const
{
	return GetCurrencyCount("Rogue_Shard", bIsInBattle);
}

bool UAwRogueDataSystem::AddCurrency_Shard(int Count, bool bIsInBattle) const
{
	return AddCurrencyCount(Count, "Rogue_Shard", bIsInBattle);
}

int UAwRogueDataSystem::GetCurrency_Coin() const
{
	return GetCurrencyCount("Rogue_Coin", true);
}

bool UAwRogueDataSystem::AddCurrency_Coin(int Count) const
{
	return AddCurrencyCount(Count, "Rogue_Coin", true);
}

void UAwRogueDataSystem::SetCurrency_Coin(int Count) const
{
	SetCurrencyCount(Count, "Rogue_Coin", true);
}

TMap<FString, int> UAwRogueDataSystem::GetAllUnlockTalent() const
{
	if (HasCurRogueData())
		return RogueSaveGame->RogueDataInfos[RogueDataIndex].UnlockTalent;
	return TMap<FString, int>();
}

int UAwRogueDataSystem::GetUnlockTalent(FString Key) const
{
	if (HasCurRogueData())
		if (RogueSaveGame->RogueDataInfos[RogueDataIndex].UnlockTalent.Contains(Key))
			return RogueSaveGame->RogueDataInfos[RogueDataIndex].UnlockTalent[Key];
	return 0;
}

void UAwRogueDataSystem::SetUnlockTalent(FString Key, int Value)
{
	if (HasCurRogueData())
		RogueSaveGame->RogueDataInfos[RogueDataIndex].UnlockTalent.Add(Key, Value);
}

void UAwRogueDataSystem::ClearUnlockTalent()
{
	if (HasCurRogueData())
		RogueSaveGame->RogueDataInfos[RogueDataIndex].UnlockTalent.Empty();
}

void UAwRogueDataSystem::SetAwakeSkillId(FString Id,int PlayerIndex)
{
	auto AwakeSkillIDs = RogueSaveGame->RogueDataInfos[RogueDataIndex].AwakeSkillId;
	while (AwakeSkillIDs.Num()<=PlayerIndex)
	{
		AwakeSkillIDs.Add(Id);
	}
	if (HasCurRogueData())
		RogueSaveGame->RogueDataInfos[RogueDataIndex].AwakeSkillId[PlayerIndex] = Id;
}

void UAwRogueDataSystem::SetUnlockAwakeSkill(TArray<FString> Ids)
{
	if (HasCurRogueData())
	{
		TSet<FString>UniqueIds = TSet<FString>(Ids);
		TArray<FString> Res = UniqueIds.Array();
		RogueSaveGame->RogueDataInfos[RogueDataIndex].UnLockAwakeSkills = Res;
	}
}

FBattleRecord UAwRogueDataSystem::GetBattleRecord() const
{
	if (HasCurRogueData())
		return RogueSaveGame->RogueDataInfos[RogueDataIndex].BattleRecord;
	return FBattleRecord();
}

int UAwRogueDataSystem::GetBattleCount(FString PawnClassId) const
{
	if (HasCurRogueData())
		if (RogueSaveGame->RogueDataInfos[RogueDataIndex].BattleRecord.ClassRecords.Contains(PawnClassId))
			return RogueSaveGame->RogueDataInfos[RogueDataIndex].BattleRecord.ClassRecords[PawnClassId].TotalGameTimes;
	return 0;
}

void UAwRogueDataSystem::AddBattleCount(FString PawnClassId)
{
	if (HasCurRogueData())
	{
		if (RogueSaveGame->RogueDataInfos[RogueDataIndex].BattleRecord.ClassRecords.Contains(PawnClassId))
		{
			RogueSaveGame->RogueDataInfos[RogueDataIndex].BattleRecord.ClassRecords[PawnClassId].TotalGameTimes += 1;
			RogueSaveGame->RogueDataInfos[RogueDataIndex].BattleRecord.GameCount += 1;
		}
	}
}

int UAwRogueDataSystem::GetClearCount(FString PawnClassId) const
{
	if (HasCurRogueData())
		if (RogueSaveGame->RogueDataInfos[RogueDataIndex].BattleRecord.ClassRecords.Contains(PawnClassId))
			return RogueSaveGame->RogueDataInfos[RogueDataIndex].BattleRecord.ClassRecords[PawnClassId].ClearedGameTimes;
	return 0;
}

int UAwRogueDataSystem::GetAllClearCount() const
{
	if (HasCurRogueData())
		return RogueSaveGame->RogueDataInfos[RogueDataIndex].BattleRecord.ClearCount;
	return 0;
}

void UAwRogueDataSystem::AddClearCount(FString PawnClassId)
{
	if (HasCurRogueData())
	{
		if (RogueSaveGame->RogueDataInfos[RogueDataIndex].BattleRecord.ClassRecords.Contains(PawnClassId))
		{
			RogueSaveGame->RogueDataInfos[RogueDataIndex].BattleRecord.ClassRecords[PawnClassId].ClearedGameTimes += 1;
			RogueSaveGame->RogueDataInfos[RogueDataIndex].BattleRecord.ClearCount += 1;
		}
	}
}

int UAwRogueDataSystem::GetBattleClearDifficulty(FString PawnClassId) const
{
	if (HasCurRogueData())
	{
		if (RogueSaveGame->RogueDataInfos[RogueDataIndex].BattleRecord.ClassRecords.Contains(PawnClassId))
		{
			return RogueSaveGame->RogueDataInfos[RogueDataIndex].BattleRecord.ClassRecords[PawnClassId].DifficultyClearLevel;
		}
	}
	return 0;
}

int UAwRogueDataSystem::GetMaxBattleClearDifficulty(FString PawnClassId) const
{
	if (HasCurRogueData())
	{
		int Max = 0;
		for(auto Record:RogueSaveGame->RogueDataInfos[RogueDataIndex].BattleRecord.ClassRecords)
		{
			Max = Max>Record.Value.DifficultyClearLevel?Max:Record.Value.DifficultyClearLevel;
		}
		return Max;
	}
	return 0;
	
}

void UAwRogueDataSystem::AddBattleClearDifficulty(FString PawnClassId)
{
	if (HasCurRogueData())
	{
		if (RogueSaveGame->RogueDataInfos[RogueDataIndex].BattleRecord.ClassRecords.Contains(PawnClassId))
		{
			RogueSaveGame->RogueDataInfos[RogueDataIndex].BattleRecord.ClassRecords[PawnClassId].DifficultyClearLevel =
				FMath::Clamp(RogueSaveGame->RogueDataInfos[RogueDataIndex].BattleRecord.ClassRecords[PawnClassId].DifficultyClearLevel+1,0,10);
		}
	}
}

void UAwRogueDataSystem::SetBattleClearDifficulty(FString PawnClassId, int NewClearDifficulty)
{
	if (HasCurRogueData())
	{
		// if (RogueSaveGame->RogueDataInfos[RogueDataIndex].BattleRecord.ClassRecords.Contains(PawnClassId))
		// {
		// 	RogueSaveGame->RogueDataInfos[RogueDataIndex].BattleRecord.ClassRecords[PawnClassId].DifficultyClearLevel = FMath::Clamp(NewClearDifficulty,0,10);
		// }
		//现在所有职业通用一个难度等级
		TArray<FString> ClassIdList;
		RogueSaveGame->RogueDataInfos[RogueDataIndex].BattleRecord.ClassRecords.GetKeys(ClassIdList);
		for (FString ClassId : ClassIdList)
		{
			RogueSaveGame->RogueDataInfos[RogueDataIndex].BattleRecord.ClassRecords[ClassId].DifficultyClearLevel = FMath::Clamp(NewClearDifficulty,0,5);
		}
	}
}

int UAwRogueDataSystem::GetDifficultyLevel_CurBattle()
{
	int CurLevel = 0;
	if (HasCurRogueData())
	{
		for (TTuple<ERogueChallengeType, int> Challenge : RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.Rogue_ChallengeList)
		{
			CurLevel += Challenge.Value;
		}
	}
	return CurLevel;
}

void UAwRogueDataSystem::SetCurBattleDataToClassRecord(bool bClearedGame)
{
	if (HasCurRogueData())
	{
		FString ClassId = RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.PawnClassId;
		if(RogueSaveGame->RogueDataInfos[RogueDataIndex].BattleRecord.ClassRecords.Contains(ClassId))
		{
			//游戏时长
			RogueSaveGame->RogueDataInfos[RogueDataIndex].BattleRecord.ClassRecords[ClassId].TotalGameTime +=
				RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.CurBattleTime;
			RogueSaveGame->RogueDataInfos[RogueDataIndex].BattleRecord.TotalGameTime +=
				RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.CurBattleTime;

			//最快通关记录
			if(bClearedGame)
			{
				if(RogueSaveGame->RogueDataInfos[RogueDataIndex].BattleRecord.ClassRecords[ClassId].FastestClearTime >
				RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.CurBattleTime ||
				UKismetMathLibrary::NearlyEqual_FloatFloat(
					RogueSaveGame->RogueDataInfos[RogueDataIndex].BattleRecord.ClassRecords[ClassId].FastestClearTime,0,0.001))
				{
					RogueSaveGame->RogueDataInfos[RogueDataIndex].BattleRecord.ClassRecords[ClassId].FastestClearTime =
						RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.CurBattleTime;
				}
				if(RogueSaveGame->RogueDataInfos[RogueDataIndex].BattleRecord.FastestClearTime >
					RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.CurBattleTime ||
					UKismetMathLibrary::NearlyEqual_FloatFloat(
						RogueSaveGame->RogueDataInfos[RogueDataIndex].BattleRecord.FastestClearTime,0,0.001))
				{
					RogueSaveGame->RogueDataInfos[RogueDataIndex].BattleRecord.FastestClearTime =
						RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.CurBattleTime;
				}
			}

			//通关时最多圣遗物数量
			if(RogueSaveGame->RogueDataInfos[RogueDataIndex].BattleRecord.ClassRecords[ClassId].MaxClearRelicNum <
				RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RelicHasGot.Num())
			{
				RogueSaveGame->RogueDataInfos[RogueDataIndex].BattleRecord.ClassRecords[ClassId].MaxClearRelicNum =
					RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RelicHasGot.Num();
			}
			if(RogueSaveGame->RogueDataInfos[RogueDataIndex].BattleRecord.MaxClearRelicNum <
				RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RelicHasGot.Num())
			{
				RogueSaveGame->RogueDataInfos[RogueDataIndex].BattleRecord.MaxClearRelicNum =
					RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RelicHasGot.Num();
			}

			//通关次数
			if(bClearedGame)
			{
				SetIsClearedGame(true);
				AddClearCount(ClassId);
			}

			//游戏次数
			AddBattleCount(ClassId);
			
			//游戏难度通关记录
			if(bClearedGame)
			{
				if(GetDifficultyLevel_CurBattle() > GetMaxBattleClearDifficulty(ClassId))
					SetBattleClearDifficulty(ClassId,GetMaxBattleClearDifficulty(ClassId)+1);
			}
			
			//经过的房间总数
			RogueSaveGame->RogueDataInfos[RogueDataIndex].BattleRecord.ClassRecords[ClassId].TotalRoomNum +=
				RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.Step;
			RogueSaveGame->RogueDataInfos[RogueDataIndex].BattleRecord.TotalRoomNum +=
				RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.Step;

			//杀死的boss数量
			RogueSaveGame->RogueDataInfos[RogueDataIndex].BattleRecord.ClassRecords[ClassId].TotalKilledBossNum +=
				RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.TotalKilledBossNum;
			RogueSaveGame->RogueDataInfos[RogueDataIndex].BattleRecord.TotalKilledBossNum +=
				RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.TotalKilledBossNum;
			
			//杀死的精英数量
			RogueSaveGame->RogueDataInfos[RogueDataIndex].BattleRecord.ClassRecords[ClassId].TotalKilledEliteNum +=
				RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.TotalKilledEliteNum;
			RogueSaveGame->RogueDataInfos[RogueDataIndex].BattleRecord.TotalKilledEliteNum +=
				RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.TotalKilledEliteNum;

			//杀死的小怪数量
			RogueSaveGame->RogueDataInfos[RogueDataIndex].BattleRecord.ClassRecords[ClassId].TotalKilledNormalNum +=
				RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.TotalKilledNormalNum;
			RogueSaveGame->RogueDataInfos[RogueDataIndex].BattleRecord.TotalKilledNormalNum +=
				RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.TotalKilledNormalNum;

			//挑战房通过次数
			RogueSaveGame->RogueDataInfos[RogueDataIndex].BattleRecord.ClassRecords[ClassId].ChallengeTimes +=
				RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.ChallengeTimes;
			RogueSaveGame->RogueDataInfos[RogueDataIndex].BattleRecord.ChallengeTimes +=
				RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.ChallengeTimes;
		}
		
	}
}

FBattleDataInfo UAwRogueDataSystem::GetCurBattleData() const
{
	if (HasCurRogueData())
		return RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo;
	return FBattleDataInfo();
}

void UAwRogueDataSystem::SetCurBattleData(FBattleDataInfo NewBattleDataInfo)
{
	if (HasCurRogueData())
		RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo = NewBattleDataInfo;
}

bool UAwRogueDataSystem::GetCurBattleDataIsActive() const
{
	if (HasCurRogueData())
		return RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.IsActive;
	return false;
}

void UAwRogueDataSystem::SetCurBattleDataIsActive(bool IsActive)
{
	if (HasCurRogueData())
		RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.IsActive = IsActive;
}

float UAwRogueDataSystem::GetCurBattleTime()
{
	if (HasCurRogueData())
		return RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.CurBattleTime;
	return 0.0f;
}

void UAwRogueDataSystem::SetCurBattleTime(float BattleTime)
{
	if (HasCurRogueData())
		RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.CurBattleTime = BattleTime;
}

void UAwRogueDataSystem::AddCurBattleKillBossNum()
{
	if (HasCurRogueData())
		RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.TotalKilledBossNum += 1;
}

void UAwRogueDataSystem::AddCurBattleKillEliteNum()
{
	if (HasCurRogueData())
		RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.TotalKilledEliteNum += 1;
}

void UAwRogueDataSystem::AddCurBattleKillNormalNum()
{
	if (HasCurRogueData())
		RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.TotalKilledNormalNum += 1;
}

void UAwRogueDataSystem::AddCurBattleChallengeTime()
{
	if (HasCurRogueData())
		RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.ChallengeTimes += 1;
}

FString UAwRogueDataSystem::GetPawnClassId_CurBattle(int PlayerIndex) const
{
	if (HasCurRogueData())
	{
		if (PlayerIndex != 1)
		{
			return RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.PawnClassId;
		}
		else
		{
			return RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.PawnClassId2;
		}
	}
	
	return "";
}

void UAwRogueDataSystem::SetPawnClassId_CurBattle(FString PawnClassId,int PlayerIndex)
{
	if (HasCurRogueData())
	{
		if (PlayerIndex != 1)
		{
			RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.PawnClassId = PawnClassId;
		}
		else
		{
			RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.PawnClassId2 = PawnClassId;
		}
	}
}
int UAwRogueDataSystem::GetRevivedChanceHasUse_CurBattle() const
{
	if (HasCurRogueData())
		return RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RevivedChanceHasUse;
	return 0;
}

void UAwRogueDataSystem::SetRevivedChanceHasUse_CurBattle(int Num)
{
	if (HasCurRogueData())
		RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RevivedChanceHasUse = Num;
}

int UAwRogueDataSystem::GetRevivedChance_CurBattle() const
{
	if (HasCurRogueData())
		return RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RevivedChance;
	return 0;
}

void UAwRogueDataSystem::SetRevivedChance_CurBattle(int Num)
{
	if (HasCurRogueData())
		RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RevivedChance = Num;
}

TMap<FString, int> UAwRogueDataSystem::GetAllRelicHasGet_CurBattle() const
{
	if (HasCurRogueData())
		return RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RelicHasGot;
	return TMap<FString, int>();
}

int UAwRogueDataSystem::GetRelicHasGet_CurBattle(FString RelicId) const
{
	if (HasCurRogueData())
		if (RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RelicHasGot.Contains(RelicId))
			return RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RelicHasGot[RelicId];
	return 0;
}

void UAwRogueDataSystem::SetRelicHasGet_CurBattle(FString RelicId, int Count)
{
	if (HasCurRogueData())
		RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RelicHasGot.Add(RelicId, Count);
}

void UAwRogueDataSystem::RemoveRelicHasGet_CurBattle(FString RelicId)
{
	if (HasCurRogueData())
		RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RelicHasGot.Remove(RelicId);
}

void UAwRogueDataSystem::ClearRelicHasGet_CurBattle()
{
	if (HasCurRogueData())
		RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RelicHasGot.Empty();
}

FAwRogueItemInfo UAwRogueDataSystem::GetHealingPotion_CurBattle() const
{
	if (HasCurRogueData())
		return RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.HealingPotion;
	return FAwRogueItemInfo();
}

void UAwRogueDataSystem::SetHealingPotion_CurBattle(FAwRogueItemInfo HealingPotion)
{
	if (HasCurRogueData())
		RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.HealingPotion = HealingPotion;
}

FAwRogueItemInfo UAwRogueDataSystem::GetCurItem_CurBattle(bool bMainItem) const
{
	if (HasCurRogueData())
		if (bMainItem)
		{
			return RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.MainItem;
		}
		else
		{
			return RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.SecondItem;
		}
	return FAwRogueItemInfo();
}

void UAwRogueDataSystem::SetCurItem_CurBattle(FAwRogueItemInfo CurItem,bool bMainItem)
{
	if (HasCurRogueData())
	{
		if (bMainItem)
		{
			RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.MainItem = CurItem;
		}
		else
		{
			RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.SecondItem = CurItem;
		}
	}
}

int UAwRogueDataSystem::GetStep_CurBattle() const
{
	if (HasCurRogueData())
		return RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.Step;
	return 0;
}

void UAwRogueDataSystem::SetStep_CurBattle(int Step)
{
	if (HasCurRogueData())
		RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.Step = Step;
}

TArray<FString> UAwRogueDataSystem::GetUsedLevelList_CurBattle() const
{
	if (HasCurRogueData())
		return RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.UsedLeveList;
	return TArray<FString>();
}

void UAwRogueDataSystem::SetUesdLevelList_CurBattle(TArray<FString> LevelList)
{
	if (HasCurRogueData())
		RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.UsedLeveList = LevelList;
}

FString UAwRogueDataSystem::GetLevelName_CurBattle() const
{
	if (HasCurRogueData())
		return RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.LevelName;
	return "";
}

void UAwRogueDataSystem::SetLevelName_CurBattle(FString LevelName)
{
	if (HasCurRogueData())
		RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.LevelName = LevelName;
}

FString UAwRogueDataSystem::GetRoomName_CurBattle() const
{
	if (HasCurRogueData())
		return RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RoomName;
	return "";
}

void UAwRogueDataSystem::SetRoomName_CurBattle(FString RoomName)
{
	if (HasCurRogueData())
		RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RoomName = RoomName;
}

ERogueRoomType UAwRogueDataSystem::GetRoomType_CurBattle() const
{
	if (HasCurRogueData())
		return RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RoomType;
	return ERogueRoomType::Normal;
}

void UAwRogueDataSystem::SetRoomType_CurBattle(ERogueRoomType RoomType)
{
	if (HasCurRogueData())
		RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RoomType = RoomType;
}

TArray<ERogueRoomReward> UAwRogueDataSystem::GetRoomReward_CurBattle() const
{
	if (HasCurRogueData())
		return RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RoomReward;
	return TArray<ERogueRoomReward>();
}

void UAwRogueDataSystem::SetRoomReward_CurBattle(TArray<ERogueRoomReward> RoomReward)
{
	if (HasCurRogueData())
		RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RoomReward = RoomReward;
}

bool UAwRogueDataSystem::GetIsClearThisRoom_CurBattle() const
{
	if (HasCurRogueData())
		return RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.IsClearThisRoom;
	return false;
}

void UAwRogueDataSystem::SetIsClearThisRoom_CurBattle(bool bClearRoom)
{
	if (HasCurRogueData())
		RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.IsClearThisRoom = bClearRoom;
}

bool UAwRogueDataSystem::GetChallengeRoomSuccess_CurBattle() const
{
	if (HasCurRogueData())
		return RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.IsChallengeRoomSuccess;
	return false;
}

void UAwRogueDataSystem::SetChallengeRoomSuccess_CurBattle(bool bSuccess)
{
	if (HasCurRogueData())
		RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.IsChallengeRoomSuccess = bSuccess;
}

int UAwRogueDataSystem::GetRandEliteRoomNum_CurBattle() const
{
	if (HasCurRogueData())
		return RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.HasRandEliteRoomNum;
	return 0;
}

void UAwRogueDataSystem::SetRandEliteRoomNum_CurBattle(int Num)
{
	if (HasCurRogueData())
		RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.HasRandEliteRoomNum = Num;
}

int UAwRogueDataSystem::GetRandChallengeRoomNum_CurBattle() const
{
	if (HasCurRogueData())
		return RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.HasRandChallengeRoomNum;
	return 0;
}

void UAwRogueDataSystem::SetRandChallengeRoomNum_CurBattle(int Num)
{
	if (HasCurRogueData())
		RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.HasRandChallengeRoomNum = Num;
}

int UAwRogueDataSystem::GetRoomRewardCoin_CurBattle() const
{
	if (HasCurRogueData())
		return RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RogueRoomReward.CoinCount;
	return 0;
}

void UAwRogueDataSystem::SetRoomRewardCoin_CurBattle(int CoinCount)
{
	if (HasCurRogueData())
		RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RogueRoomReward.CoinCount = CoinCount;
}

int UAwRogueDataSystem::GetRoomRewardExtraCoin_CurBattle() const
{
	if (HasCurRogueData())
		return RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RogueRoomReward.ExtraCoinCount;
	return 0;
}

void UAwRogueDataSystem::SetRoomRewardExtraCoin_CurBattle(int CoinCount)
{
	if (HasCurRogueData())
		RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RogueRoomReward.ExtraCoinCount = CoinCount;
}

int UAwRogueDataSystem::GetRoomRewardKey_CurBattle() const
{
	if (HasCurRogueData())
		return RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RogueRoomReward.KeyCount;
	return 0;
}

void UAwRogueDataSystem::SetRoomRewardKey_CurBattle(int KeyCount)
{
	if (HasCurRogueData())
		RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RogueRoomReward.KeyCount = KeyCount;
}

int UAwRogueDataSystem::GetRoomRewardSoul_CurBattle() const
{
	if (HasCurRogueData())
		return RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RogueRoomReward.SoulCount;
	return 0;
}

void UAwRogueDataSystem::SetRoomRewardSoul_CurBattle(int SoulCount)
{
	if (HasCurRogueData())
	{
		RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RogueRoomReward.SoulCount = SoulCount;
		FString str = FString::FromInt(RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RogueRoomReward.SoulCount);
		UKismetSystemLibrary::PrintString(GWorld,str);
	}
	
}

int UAwRogueDataSystem::GetRoomRewardShard_CurBattle() const
{
	if (HasCurRogueData())
		return RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RogueRoomReward.ShardCount;
	return 0;
}

void UAwRogueDataSystem::SetRoomRewardShard_CurBattle(int ShardCount)
{
	if (HasCurRogueData())
		RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RogueRoomReward.ShardCount = ShardCount;
}

bool UAwRogueDataSystem::HasGotRelicReward_CurRoom() const
{
	if (HasCurRogueData())
	{
		for (FRandomReward Reward : RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RogueRoomReward.Rewards)
		{
			if(Reward.RewardType == 0)
			{
				return Reward.IsGot;
			}
		}
	}
	return false;
}

void UAwRogueDataSystem::SetHasGotRelicReward_CurRoom(bool IsGot)
{
	if (HasCurRogueData())
	{
		if (HasCurRogueData())
		{
			for (int i = 0; i < RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RogueRoomReward.Rewards.Num(); i++)
			{
				if(RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RogueRoomReward.Rewards[i].RewardType == 0)
				{
					RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RogueRoomReward.Rewards[i].IsGot = IsGot;
					return;
				}
			}
		}
	}
}

bool UAwRogueDataSystem::GetHasRelicReward_CurRoom() const
{
	if (HasCurRogueData())
	{
		for (FRandomReward Reward : RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RogueRoomReward.Rewards)
		{
			if(Reward.RewardType == 0)
			{
				return Reward.Rewards.Num() > 0;
			}
		}
	}
	return false;	
}

TMap<FString,int> UAwRogueDataSystem::GetRelicReward_CurRoom() const
{
	if (HasCurRogueData())
	{
		for (FRandomReward Reward : RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RogueRoomReward.Rewards)
		{
			if(Reward.RewardType == 0)
			{
				return Reward.Rewards;
			}
		}
	}
	return TMap<FString,int>();
}

void UAwRogueDataSystem::SetRelicReward_CurRoom(TMap<FString,int> RelicList)
{
	if (HasCurRogueData())
	{
		for (int i = 0; i < RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RogueRoomReward.Rewards.Num(); i++)
		{
			if(RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RogueRoomReward.Rewards[i].RewardType == 0)
			{
				RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RogueRoomReward.Rewards[i].Rewards = RelicList;
			}
		}
	}
}

void UAwRogueDataSystem::ClearRelicReward_CurRoom()
{
	if (HasCurRogueData())
	{
		for (int i = 0; i < RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RogueRoomReward.Rewards.Num(); i++)
		{
			if(RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RogueRoomReward.Rewards[i].RewardType == 0)
			{
				RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RogueRoomReward.Rewards[i].Rewards.Empty();
			}
		}
	}
}

bool UAwRogueDataSystem::HasGotItemReward_CurRoom() const
{
	if (HasCurRogueData())
	{
		for (FRandomReward Reward : RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RogueRoomReward.Rewards)
		{
			if(Reward.RewardType == 1)
			{
				return Reward.IsGot;
			}
		}
	}
	return false;
}

void UAwRogueDataSystem::SetHasGotItemReward_CurRoom(bool IsGot)
{
	if (HasCurRogueData())
	{
		for (int i = 0; i < RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RogueRoomReward.Rewards.Num(); i++)
		{
			if(RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RogueRoomReward.Rewards[i].RewardType == 1)
			{
				RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RogueRoomReward.Rewards[i].IsGot = IsGot;
				return;
			}
		}
	}
}

bool UAwRogueDataSystem::GetHasItemReward_CurRoom() const
{
	if (HasCurRogueData())
	{
		for (FRandomReward Reward : RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RogueRoomReward.Rewards)
		{
			if(Reward.RewardType == 1)
			{
				return Reward.Rewards.Num() > 0;
			}
		}
	}
	return false;	
}

TMap<FString,int> UAwRogueDataSystem::GetItemReward_CurRoom() const
{
	if (HasCurRogueData())
	{
		for (FRandomReward Reward : RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RogueRoomReward.Rewards)
		{
			if(Reward.RewardType == 1)
			{
				return Reward.Rewards;
			}
		}
	}
	return TMap<FString,int>();
}

void UAwRogueDataSystem::SetItemReward_CurRoom(TMap<FString,int> ItemList)
{
	if (HasCurRogueData())
	{
		for (int i = 0; i < RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RogueRoomReward.Rewards.Num(); i++)
		{
			if(RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RogueRoomReward.Rewards[i].RewardType == 1)
			{
				RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RogueRoomReward.Rewards[i].Rewards = ItemList;
			}
		}
	}
}

void UAwRogueDataSystem::ClearItemReward_CurRoom()
{
	if (HasCurRogueData())
	{
		for (int i = 0; i < RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RogueRoomReward.Rewards.Num(); i++)
		{
			if(RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RogueRoomReward.Rewards[i].RewardType == 1)
			{
				RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RogueRoomReward.Rewards[i].Rewards.Empty();
			}
		}
	}
}

bool UAwRogueDataSystem::HasGotActionReward_CurRoom() const
{
	if (HasCurRogueData())
	{
		for (FRandomReward Reward : RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RogueRoomReward.Rewards)
		{
			if(Reward.RewardType == 2)
			{
				return Reward.IsGot;
			}
		}
	}
	return false;
}

void UAwRogueDataSystem::SetHasGotActionReward_CurRoom(bool IsGot)
{
	if (HasCurRogueData())
	{
		for (int i = 0; i < RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RogueRoomReward.Rewards.Num(); i++)
		{
			if(RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RogueRoomReward.Rewards[i].RewardType == 2)
			{
				RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RogueRoomReward.Rewards[i].IsGot = IsGot;
				return;
			}
		}
	}
}

bool UAwRogueDataSystem::GetHasActionReward_CurRoom() const
{
	if (HasCurRogueData())
	{
		for (FRandomReward Reward : RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RogueRoomReward.Rewards)
		{
			if(Reward.RewardType == 2)
			{
				return Reward.ActionRewards.Num() > 0;
			}
		}
	}
	return false;	
}

TArray<FRougeAbilityLevelInfo> UAwRogueDataSystem::GetActionReward_CurRoom() const
{
	if (HasCurRogueData())
	{
		for (FRandomReward Reward : RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RogueRoomReward.Rewards)
		{
			if(Reward.RewardType == 2)
			{
				return Reward.ActionRewards;
			}
		}
	}
	return TArray<FRougeAbilityLevelInfo>();
}

void UAwRogueDataSystem::SetActionReward_CurRoom(TArray<FRougeAbilityLevelInfo> ActionList)
{
	if (HasCurRogueData())
	{
		for (int i = 0; i < RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RogueRoomReward.Rewards.Num(); i++)
		{
			if(RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RogueRoomReward.Rewards[i].RewardType == 2)
			{
				RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RogueRoomReward.Rewards[i].ActionRewards = ActionList;
			}
		}
	}
}

void UAwRogueDataSystem::ClearActionReward_CurRoom()
{
	if (HasCurRogueData())
	{
		for (int i = 0; i < RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RogueRoomReward.Rewards.Num(); i++)
		{
			if(RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RogueRoomReward.Rewards[i].RewardType == 2)
			{
				RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RogueRoomReward.Rewards[i].ActionRewards.Empty();
			}
		}
	}
}

bool UAwRogueDataSystem::GetShopIsActive_CurBattle() const
{
	if (HasCurRogueData())
		return RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RogueRoomReward.ShopInfo.isActive;
	return false;
}

void UAwRogueDataSystem::SetShopIsActive_CurBattle(bool IsActive)
{
	if (HasCurRogueData())
		RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RogueRoomReward.ShopInfo.isActive = IsActive;
}

FRogueShopReward UAwRogueDataSystem::GetShopInfo_CurBattle() const
{
	if (HasCurRogueData())
		return RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RogueRoomReward.ShopInfo;
	return FRogueShopReward();
}

void UAwRogueDataSystem::SetShopInfo_CurBattle(FRogueShopReward ShopInfo)
{
	if (HasCurRogueData())
		RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RogueRoomReward.ShopInfo = ShopInfo;
}

FString UAwRogueDataSystem::GetBattleStyle(FString PawnClassId) const
{
	if (HasCurRogueData())
	{
		if (RogueSaveGame->RogueDataInfos[RogueDataIndex].BattleStyle.Contains(PawnClassId))
			return RogueSaveGame->RogueDataInfos[RogueDataIndex].BattleStyle[PawnClassId];
	}
	return "";
}

void UAwRogueDataSystem::SetBattleStyle(FString PawnClassId, FString StyleId) const
{
	if (HasCurRogueData())
		RogueSaveGame->RogueDataInfos[RogueDataIndex].BattleStyle.Add(PawnClassId, StyleId);
}

void UAwRogueDataSystem::SetPrayReward_CurRoom(TMap<FString,FRandomReward> PrayReward)
{
	if (HasCurRogueData())
	{
		RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RogueRoomReward.PrayReward = PrayReward;
	}
}

TMap<FString, FRandomReward> UAwRogueDataSystem::GetPrayReward_CurRoom() const
{
	if (HasCurRogueData())
	{
		return RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RogueRoomReward.PrayReward;
	}
	return TMap<FString,FRandomReward>();
}

bool UAwRogueDataSystem::GetHasPrayReward_CurRoom() const
{
	if (HasCurRogueData())
	{
		return RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RogueRoomReward.PrayReward.Num() > 0;
	}
	return false;
}

void UAwRogueDataSystem::ClearPrayReward_CurRoom()
{
	if (HasCurRogueData())
	{
		RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RogueRoomReward.PrayReward.Empty();
	}
}

void UAwRogueDataSystem::SetUpgradeHealingPotionIsActive_CurRoom(bool isActive)
{
	if (HasCurRogueData())
	{
		RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RogueRoomReward.UpgradeHealingPotionIsActive = isActive;
	}
}

bool UAwRogueDataSystem::GetUpgradeHealingPotionIsActive_CurRoom() const
{
	if (HasCurRogueData())
	{
		return RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RogueRoomReward.UpgradeHealingPotionIsActive;
	}
	return false;
}

void UAwRogueDataSystem::SetPrayRewardsIsActive_CurRoom(bool isActive)
{
	if (HasCurRogueData())
	{
		RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RogueRoomReward.PrayRewardsIsActive = isActive;
	}
}

bool UAwRogueDataSystem::GetPrayRewardsIsActive_CurRoom() const
{
	if (HasCurRogueData())
	{
		return RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.RogueRoomReward.PrayRewardsIsActive;
	}
	return false;
}

TArray<FRogueRoomInfo> UAwRogueDataSystem::GetNextRoomList_CurRoom() const
{
	if (HasCurRogueData())
	{
		return RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.NextRoomList;
	}
	return TArray<FRogueRoomInfo>();
}

void UAwRogueDataSystem::SetNextRoomList_CurRoom(TArray<FRogueRoomInfo> RoomList)
{
	if (HasCurRogueData())
		RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.NextRoomList = RoomList;
}

void UAwRogueDataSystem::ClearNextRoomList_CurRoom()
{
	if (HasCurRogueData())
		RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.NextRoomList.Empty();
}

void UAwRogueDataSystem::SetHP_CurBattle(int hp)
{
	if (HasCurRogueData())
		RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.HP = hp;
}

int UAwRogueDataSystem::GetHP_CurBattle() const
{
  	if (HasCurRogueData())
	{
		return RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.HP;
	}
	return 0;
}

void UAwRogueDataSystem::SetAP_CurBattle(int ap)
{
	if (HasCurRogueData())
		RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.AP = ap;
}

int UAwRogueDataSystem::GetAP_CurBattle() const
{
	if (HasCurRogueData())
	{
		return RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.AP;
	}
	return 0;
}

void UAwRogueDataSystem::SetItemEnergy_CurBattle(int Energy)
{
	if (HasCurRogueData())
		RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.ItemEnergy = Energy;
}

int UAwRogueDataSystem::GetItemEnergy_CurBattle() const
{
	if (HasCurRogueData())
	{
		return RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.ItemEnergy;
	}
	return 0;
}

void UAwRogueDataSystem::SetHasSelectStartBless(bool bSelected )
{
	if (HasCurRogueData())
		RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.SelectedStartBless = bSelected;
}

bool UAwRogueDataSystem::GetHasSelectStartBless() const
{
	if (HasCurRogueData())
	{
		return RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.SelectedStartBless;
	}	
	return false;
}

void UAwRogueDataSystem::AddUpgrade(FString StyleId, FString UpgradeId)
{
	if (HasCurRogueData())
	{
		if (!RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.BattleStyleUpgradeInfo.Contains(StyleId))
			RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.BattleStyleUpgradeInfo.Add(StyleId, FUpgradeInfos());
		RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.BattleStyleUpgradeInfo[StyleId].Ids.Add(UpgradeId);
	}
}

void UAwRogueDataSystem::RemoveUpgrade(FString StyleId, FString UpgradeId)
{
	if (HasCurRogueData())
	{
		if (RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.BattleStyleUpgradeInfo.Contains(StyleId))
			RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.BattleStyleUpgradeInfo[StyleId].Ids.Remove(UpgradeId);
	}
}

void UAwRogueDataSystem::ClearUpgrade(FString StyleId)
{
	if (HasCurRogueData())
	{
		if (RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.BattleStyleUpgradeInfo.Contains(StyleId))
			RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.BattleStyleUpgradeInfo.Remove(StyleId);
	}
}

TArray<FString> UAwRogueDataSystem::GetUpgrades(FString StyleId) const
{
	TArray<FString> Res;
	if (HasCurRogueData())
	{
		if (RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.BattleStyleUpgradeInfo.Contains(StyleId))
			Res = RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.BattleStyleUpgradeInfo[StyleId].Ids;
	}

	return Res;
}

int UAwRogueDataSystem::GetBattleStyleUpgradePoint() const
{
	if (HasCurRogueData())
		return RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.BattleStyleUpgradeReward.Point;
	return 0;
}

void UAwRogueDataSystem::SetBattleStyleUpgradePoint(const int NewCount)
{
	if (HasCurRogueData())
		RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.BattleStyleUpgradeReward.Point = NewCount;
}

int UAwRogueDataSystem::GetBattleStyleUpgradeMaxPoint() const
{
	if (HasCurRogueData())
		return RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.BattleStyleUpgradeReward.MaxPoint;
	return 1;
}

void UAwRogueDataSystem::SetBattleStyleUpgradeMaxPoint(int NewCount)
{
	if (HasCurRogueData())
		RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.BattleStyleUpgradeReward.MaxPoint = NewCount;
}

TArray<FString> UAwRogueDataSystem::GetBattleStyleUpgradeRandomReward() const
{
	if (HasCurRogueData())
		return RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.BattleStyleUpgradeReward.RandomUpgradeIds;
	return TArray<FString>();
}

void UAwRogueDataSystem::SetBattleStyleUpgradeRandomReward(const TArray<FString> NewRandomRewards)
{
	if (HasCurRogueData())
		RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.BattleStyleUpgradeReward.RandomUpgradeIds = NewRandomRewards;
}

bool UAwRogueDataSystem::GetBattleStyleUpgradeHasGot() const
{
	if (HasCurRogueData())
		return RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.BattleStyleUpgradeReward.HasGot;
	return false;
}

void UAwRogueDataSystem::SetBattleStyleUpgradeHasGot(bool HasGot)
{
	if (HasCurRogueData())
		RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.BattleStyleUpgradeReward.HasGot = HasGot;
}

bool UAwRogueDataSystem::GetBattleStyleUpgradeHasGotPoint() const
{
	if (HasCurRogueData())
		return RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.BattleStyleUpgradeReward.HasGotPoint;
	return false;
}

void UAwRogueDataSystem::SetBattleStyleUpgradeHasGotPoint(bool HasGot)
{
	if (HasCurRogueData())
		RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.BattleStyleUpgradeReward.HasGotPoint = HasGot;
}

FBattleAbilityIds UAwRogueDataSystem::GetCurBattleAbilityIds(int PlayerIndex) const
{
	if (HasCurRogueData())
	{
		FBattleAbilityIds AbilityIds = FBattleAbilityIds();
		FBattleAbilityIds DefaultAbilityIds = GetDefaultBattleAbilityIds(GetCurPawnClassId(PlayerIndex));
		if (RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.AbilityLevelInfos.Contains(ERogueAbilitySlot::NormalAttack))
			AbilityIds.NormalAttack = RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.AbilityLevelInfos[ERogueAbilitySlot::NormalAttack].AbilityInfoId;
		else
			AbilityIds.NormalAttack = DefaultAbilityIds.NormalAttack;
		if (RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.AbilityLevelInfos.Contains(ERogueAbilitySlot::Ground1))
			AbilityIds.Ground1 = RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.AbilityLevelInfos[ERogueAbilitySlot::Ground1].AbilityInfoId;
		else
			AbilityIds.Ground1 = DefaultAbilityIds.Ground1;
		if (RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.AbilityLevelInfos.Contains(ERogueAbilitySlot::Ground2))
			AbilityIds.Ground2 = RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.AbilityLevelInfos[ERogueAbilitySlot::Ground2].AbilityInfoId;
		else
			AbilityIds.Ground2 = DefaultAbilityIds.Ground2;
		if (RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.AbilityLevelInfos.Contains(ERogueAbilitySlot::Air1))
			AbilityIds.Air1 = RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.AbilityLevelInfos[ERogueAbilitySlot::Air1].AbilityInfoId;
		else
			AbilityIds.Air1 = DefaultAbilityIds.Air1;
		if (RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.AbilityLevelInfos.Contains(ERogueAbilitySlot::Air2))
			AbilityIds.Air2 = RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.AbilityLevelInfos[ERogueAbilitySlot::Air2].AbilityInfoId;
		else
			AbilityIds.Air2 = DefaultAbilityIds.Air2;

		return AbilityIds;
	} 
	
	return FBattleAbilityIds();
}

FString UAwRogueDataSystem::GetCurBattleAbilityId(ERogueAbilitySlot AbilitySlot,const int PlayerIndex) const
{
	if(RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.AbilityLevelInfos.Contains(AbilitySlot))
	{
		return RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.AbilityLevelInfos[AbilitySlot].AbilityInfoId;
	}
	return "";
	// FBattleAbilityIds Ids = GetCurBattleAbilityIds(PlayerIndex);
	// switch (AbilitySlot)
	// {
	// case ERogueAbilitySlot::NormalAttack: return Ids.NormalAttack;
	// case ERogueAbilitySlot::Ground1: return Ids.Ground1;
	// case ERogueAbilitySlot::Ground2: return Ids.Ground2;
	// case ERogueAbilitySlot::Air1: return Ids.Air1;
	// case ERogueAbilitySlot::Air2: return Ids.Air2;
	// default: return "";
	// }
}

void UAwRogueDataSystem::SetCurBattleAbilityIds(FBattleAbilityIds BattleAbilityIds,const int PlayerIndex)
{
	if (HasCurRogueData())
		RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.CurBattleAbilityIds = BattleAbilityIds;
}

void UAwRogueDataSystem::SetCurBattleAbilityId(ERogueAbilitySlot AbilitySlot, FString AbilityId,const int PlayerIndex)
{
	if (HasCurRogueData())
	{
		switch (AbilitySlot)
		{
		case ERogueAbilitySlot::None:
			break;
		case ERogueAbilitySlot::NormalAttack:
			RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.CurBattleAbilityIds.NormalAttack = AbilityId;
			break;
		case ERogueAbilitySlot::Ground1:
			RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.CurBattleAbilityIds.Ground1 = AbilityId;
			break;
		case ERogueAbilitySlot::Ground2:
			RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.CurBattleAbilityIds.Ground2 = AbilityId;
			break;
		case ERogueAbilitySlot::Air1:
			RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.CurBattleAbilityIds.Air1 = AbilityId;
			break;
		case ERogueAbilitySlot::Air2:
			RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.CurBattleAbilityIds.Air2 = AbilityId;
			break;
		default: break;
		}
	}
}

void UAwRogueDataSystem::EmptyCurBattleAbilityId(const int PlayerIndex)
{
	if (HasCurRogueData())
		SetCurBattleAbilityIds(FBattleAbilityIds(),PlayerIndex);
}

TMap<ERogueAbilitySlot, FRougeAbilityLevelInfo> UAwRogueDataSystem::GetCurAbilityLevelInfos(const FString& PawnClassId) const
{
	TMap<ERogueAbilitySlot, FRougeAbilityLevelInfo> Res;
	if (HasCurRogueData())
		Res = RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.AbilityLevelInfos;

	if(RogueSaveGame->RogueDataInfos[RogueDataIndex].PreviewAbilityInfos.Contains(PawnClassId))
	{
		if (!Res.Contains(ERogueAbilitySlot::NormalAttack))
			if(!GetPreviewAbilityLevelInfo(PawnClassId, ERogueAbilitySlot::NormalAttack).AbilityInfoId.IsEmpty())
				Res.Add(ERogueAbilitySlot::NormalAttack, GetPreviewAbilityLevelInfo(PawnClassId, ERogueAbilitySlot::NormalAttack));
			else
				Res.Add(ERogueAbilitySlot::NormalAttack, GetDefaultAbilityLevelInfo(PawnClassId, ERogueAbilitySlot::NormalAttack));
	
		if (!Res.Contains(ERogueAbilitySlot::Ground1))
			if(!GetPreviewAbilityLevelInfo(PawnClassId, ERogueAbilitySlot::Ground1).AbilityInfoId.IsEmpty())
				Res.Add(ERogueAbilitySlot::Ground1, GetPreviewAbilityLevelInfo(PawnClassId, ERogueAbilitySlot::Ground1));
			else
				Res.Add(ERogueAbilitySlot::Ground1, GetDefaultAbilityLevelInfo(PawnClassId, ERogueAbilitySlot::Ground1));
			
		if (!Res.Contains(ERogueAbilitySlot::Ground2))
			if(!GetPreviewAbilityLevelInfo(PawnClassId, ERogueAbilitySlot::Ground2).AbilityInfoId.IsEmpty())
				Res.Add(ERogueAbilitySlot::Ground2, GetPreviewAbilityLevelInfo(PawnClassId, ERogueAbilitySlot::Ground2));
			else
				Res.Add(ERogueAbilitySlot::Ground2, GetDefaultAbilityLevelInfo(PawnClassId, ERogueAbilitySlot::Ground2));
	
		if (!Res.Contains(ERogueAbilitySlot::Air1))
			if(!GetPreviewAbilityLevelInfo(PawnClassId, ERogueAbilitySlot::Air1).AbilityInfoId.IsEmpty())
				Res.Add(ERogueAbilitySlot::Air1, GetPreviewAbilityLevelInfo(PawnClassId, ERogueAbilitySlot::Air1));
			else
				Res.Add(ERogueAbilitySlot::Air1, GetDefaultAbilityLevelInfo(PawnClassId, ERogueAbilitySlot::Air1));
	
		if (!Res.Contains(ERogueAbilitySlot::Air2))
			if(!GetPreviewAbilityLevelInfo(PawnClassId, ERogueAbilitySlot::Air2).AbilityInfoId.IsEmpty())
				Res.Add(ERogueAbilitySlot::Air2, GetPreviewAbilityLevelInfo(PawnClassId, ERogueAbilitySlot::Air2));
			else
				Res.Add(ERogueAbilitySlot::Air2, GetDefaultAbilityLevelInfo(PawnClassId, ERogueAbilitySlot::Air2));
	}
	else
	{
		if (!Res.Contains(ERogueAbilitySlot::NormalAttack))
			Res.Add(ERogueAbilitySlot::NormalAttack, GetDefaultAbilityLevelInfo(PawnClassId, ERogueAbilitySlot::NormalAttack));
	
		if (!Res.Contains(ERogueAbilitySlot::Ground1))
			Res.Add(ERogueAbilitySlot::Ground1, GetDefaultAbilityLevelInfo(PawnClassId, ERogueAbilitySlot::Ground1));
	
		if (!Res.Contains(ERogueAbilitySlot::Ground2))
			Res.Add(ERogueAbilitySlot::Ground2, GetDefaultAbilityLevelInfo(PawnClassId, ERogueAbilitySlot::Ground2));
	
		if (!Res.Contains(ERogueAbilitySlot::Air1))
			Res.Add(ERogueAbilitySlot::Air1, GetDefaultAbilityLevelInfo(PawnClassId, ERogueAbilitySlot::Air1));
	
		if (!Res.Contains(ERogueAbilitySlot::Air2))
			Res.Add(ERogueAbilitySlot::Air2, GetDefaultAbilityLevelInfo(PawnClassId, ERogueAbilitySlot::Air2));
	}
	
	
	return Res;
}

FRougeAbilityLevelInfo UAwRogueDataSystem::GetCurAbilityLevelInfo(ERogueAbilitySlot AbilitySlot, const FString& PawnClassId) const
{
	FRougeAbilityLevelInfo Res;
	
	if (HasCurRogueData() && RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.AbilityLevelInfos.Contains(AbilitySlot))
		Res = RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.AbilityLevelInfos[AbilitySlot];
	else
	{
		Res = GetDefaultAbilityLevelInfo(PawnClassId, AbilitySlot);
	}

	return Res;
}

void UAwRogueDataSystem::SetCurAbilityLevelInfos(TMap<ERogueAbilitySlot, FRougeAbilityLevelInfo> AbilityLevelInfos)
{
	if (HasCurRogueData())
		RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.AbilityLevelInfos = AbilityLevelInfos;
}

void UAwRogueDataSystem::SetCurAbilityLevelInfo(ERogueAbilitySlot AbilitySlot, FRougeAbilityLevelInfo AbilityLevelInfo)
{
	if (HasCurRogueData())
		if (RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.AbilityLevelInfos.Contains(AbilitySlot))
			RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.AbilityLevelInfos[AbilitySlot] = AbilityLevelInfo;
		else
			RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.AbilityLevelInfos.Add(AbilitySlot, AbilityLevelInfo);
}

void UAwRogueDataSystem::EmptyCurAbilityLevelInfos()
{
	if (HasCurRogueData())
		RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.AbilityLevelInfos.Empty();
}

TArray<FString> UAwRogueDataSystem::GetGiveUpBattleAbilityIds() const
{
	if (HasCurRogueData())
		return RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.GiveUpBattleAbilityIds;
	return TArray<FString>();
}

void UAwRogueDataSystem::AddGiveUpBattleAbilityId(FString AbilityId)
{
	if (HasCurRogueData() &&
		!RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.GiveUpBattleAbilityIds.Contains(AbilityId)) 
		RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.GiveUpBattleAbilityIds.Add(AbilityId);
}

void UAwRogueDataSystem::ClearGiveUpBattleAbilityIds() const
{
	if (HasCurRogueData())
		RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.GiveUpBattleAbilityIds.Empty();
}

TMap<FString, int> UAwRogueDataSystem::GetBattleUpgradeIds() const
{
	if (HasCurRogueData())
		return RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.BattleUpgradeIds;
	return TMap<FString, int>();
}

int UAwRogueDataSystem::GetBattleUpgradeLevel(FString UpgradeId) const
{
	if (HasCurRogueData() && RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.BattleUpgradeIds.Contains(UpgradeId))
			return RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.BattleUpgradeIds[UpgradeId];
	return -1;
}

void UAwRogueDataSystem::SetBattleUpgradeId(FString UpgradeId, int Level,const FString& PawnClassId)
{
	if (HasCurRogueData())
	{
		if (RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.BattleUpgradeIds.Contains(UpgradeId))
		{
			const int MaxLevel = UGameplayFuncLib::GetDataManager()->GetRogueBattleUpgradeInfo(PawnClassId, UpgradeId).MaxLevel;
			const int NewLevel = FMath::Clamp(Level, 0, MaxLevel);
			RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.BattleUpgradeIds.Add(UpgradeId, NewLevel);
		}
		else
			RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.BattleUpgradeIds.Add(UpgradeId, 0);
	}
}

void UAwRogueDataSystem::RemoveBattleUpgradeId(FString UpgradeId)
{
	if (HasCurRogueData())
		RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.BattleUpgradeIds.Remove(UpgradeId);
}

void UAwRogueDataSystem::ClearBattleUpgradeIds()
{
	if (HasCurRogueData())
		RogueSaveGame->RogueDataInfos[RogueDataIndex].CurBattleDataInfo.BattleUpgradeIds.Empty();
}

FBattleAbilityIds UAwRogueDataSystem::GetDefaultBattleAbilityIds(const FString& PawnClassId)
{
	FBattleAbilityIds Res;
	FRogueBattleUpgrade BattleUpgrade = UGameplayFuncLib::GetDataManager()->GetRogueBattleUpgrade(PawnClassId);
			
	Res.NormalAttack = BattleUpgrade.DefaultNormalAttack;
	Res.Ground1 = BattleUpgrade.DefaultGround1;
	Res.Ground2 = BattleUpgrade.DefaultGround2;
	Res.Air1 = BattleUpgrade.DefaultAir1;
	Res.Air2 = BattleUpgrade.DefaultAir2;
	return Res;
}

TMap<ERogueAbilitySlot, FRougeAbilityLevelInfo> UAwRogueDataSystem::GetDefaultAbilityLevelInfos(const FString& PawnClassId)
{
	TMap<ERogueAbilitySlot, FRougeAbilityLevelInfo> Res;
	
	// const FString PawnClassId = GetCurPawnClassId(PlayerIndex);
	Res.Add(ERogueAbilitySlot::NormalAttack, GetDefaultAbilityLevelInfo(PawnClassId, ERogueAbilitySlot::NormalAttack));
	Res.Add(ERogueAbilitySlot::Ground1, GetDefaultAbilityLevelInfo(PawnClassId, ERogueAbilitySlot::Ground1));
	Res.Add(ERogueAbilitySlot::Ground2, GetDefaultAbilityLevelInfo(PawnClassId, ERogueAbilitySlot::Ground2));
	Res.Add(ERogueAbilitySlot::Air1, GetDefaultAbilityLevelInfo(PawnClassId, ERogueAbilitySlot::Air1));
	Res.Add(ERogueAbilitySlot::Air2, GetDefaultAbilityLevelInfo(PawnClassId, ERogueAbilitySlot::Air2));
	
	return Res;
}

TMap<ERogueAbilitySlot, FRougeAbilityLevelInfo> UAwRogueDataSystem::GetDefaultAbilityLevelInfosByClassId(const FString& ClassId)
{
	TMap<ERogueAbilitySlot, FRougeAbilityLevelInfo> Res;
	
	Res.Add(ERogueAbilitySlot::NormalAttack, GetDefaultAbilityLevelInfo(ClassId, ERogueAbilitySlot::NormalAttack));
	Res.Add(ERogueAbilitySlot::Ground1, GetDefaultAbilityLevelInfo(ClassId, ERogueAbilitySlot::Ground1));
	Res.Add(ERogueAbilitySlot::Ground2, GetDefaultAbilityLevelInfo(ClassId, ERogueAbilitySlot::Ground2));
	Res.Add(ERogueAbilitySlot::Air1, GetDefaultAbilityLevelInfo(ClassId, ERogueAbilitySlot::Air1));
	Res.Add(ERogueAbilitySlot::Air2, GetDefaultAbilityLevelInfo(ClassId, ERogueAbilitySlot::Air2));
	
	return Res;
}

FRougeAbilityLevelInfo UAwRogueDataSystem::GetDefaultAbilityLevelInfo(const FString& PawnClassId,
                                                                      ERogueAbilitySlot AbilitySlot)
{
	FRougeAbilityLevelInfo Res;
	FRogueBattleUpgrade BattleUpgrade = UGameplayFuncLib::GetDataManager()->GetRogueBattleUpgrade(PawnClassId);
		
	switch (AbilitySlot)
	{
	case ERogueAbilitySlot::None: break;
	case ERogueAbilitySlot::NormalAttack: Res.AbilityInfoId = BattleUpgrade.DefaultNormalAttack; break;
	case ERogueAbilitySlot::Ground1: Res.AbilityInfoId = BattleUpgrade.DefaultGround1; break;
	case ERogueAbilitySlot::Ground2: Res.AbilityInfoId = BattleUpgrade.DefaultGround2; break;
	case ERogueAbilitySlot::Air1: Res.AbilityInfoId = BattleUpgrade.DefaultAir1; break;
	case ERogueAbilitySlot::Air2: Res.AbilityInfoId = BattleUpgrade.DefaultAir2; break;
	default: ;
	}
	return Res;
}

TMap<ERogueAbilitySlot, FRougeAbilityLevelInfo> UAwRogueDataSystem::GetPreviewAbilityLevelInfos(const FString& PawnClassId) const
{
	TMap<ERogueAbilitySlot, FRougeAbilityLevelInfo> Res;
	
	if(GetPreviewAbilityLevelInfo(PawnClassId, ERogueAbilitySlot::NormalAttack).AbilityInfoId.IsEmpty())
	{
		Res.Add(ERogueAbilitySlot::NormalAttack, GetDefaultAbilityLevelInfo(PawnClassId, ERogueAbilitySlot::NormalAttack));
	}
	else
	{
		Res.Add(ERogueAbilitySlot::NormalAttack, GetPreviewAbilityLevelInfo(PawnClassId, ERogueAbilitySlot::NormalAttack));
	}
	if(GetPreviewAbilityLevelInfo(PawnClassId, ERogueAbilitySlot::Ground1).AbilityInfoId.IsEmpty())
	{
		Res.Add(ERogueAbilitySlot::Ground1, GetDefaultAbilityLevelInfo(PawnClassId, ERogueAbilitySlot::Ground1));
	}
	else
	{
		Res.Add(ERogueAbilitySlot::Ground1, GetPreviewAbilityLevelInfo(PawnClassId, ERogueAbilitySlot::Ground1));
	}
	if(GetPreviewAbilityLevelInfo(PawnClassId, ERogueAbilitySlot::Ground2).AbilityInfoId.IsEmpty())
	{
		Res.Add(ERogueAbilitySlot::Ground2, GetDefaultAbilityLevelInfo(PawnClassId, ERogueAbilitySlot::Ground2));
	}
	else
	{
		Res.Add(ERogueAbilitySlot::Ground2, GetPreviewAbilityLevelInfo(PawnClassId, ERogueAbilitySlot::Ground2));
	}
	if(GetPreviewAbilityLevelInfo(PawnClassId, ERogueAbilitySlot::Air1).AbilityInfoId.IsEmpty())
	{
		Res.Add(ERogueAbilitySlot::Air1, GetDefaultAbilityLevelInfo(PawnClassId, ERogueAbilitySlot::Air1));
	}
	else
	{
		Res.Add(ERogueAbilitySlot::Air1, GetPreviewAbilityLevelInfo(PawnClassId, ERogueAbilitySlot::Air1));
	}
	if(GetPreviewAbilityLevelInfo(PawnClassId, ERogueAbilitySlot::Air2).AbilityInfoId.IsEmpty())
	{
		Res.Add(ERogueAbilitySlot::Air2, GetDefaultAbilityLevelInfo(PawnClassId, ERogueAbilitySlot::Air2));
	}
	else
	{
		Res.Add(ERogueAbilitySlot::Air2, GetPreviewAbilityLevelInfo(PawnClassId, ERogueAbilitySlot::Air2));
	}
	
	return Res;
}

FRougeAbilityLevelInfo UAwRogueDataSystem::GetPreviewAbilityLevelInfo(const FString& PawnClassId,
	ERogueAbilitySlot AbilitySlot) const
{
	FRougeAbilityLevelInfo Res;
	if (HasCurRogueData())
	{
		if (RogueSaveGame->RogueDataInfos[RogueDataIndex].PreviewAbilityInfos.Contains(PawnClassId))
		{
			switch (AbilitySlot)
			{
			case ERogueAbilitySlot::None: break;
			case ERogueAbilitySlot::NormalAttack: Res.AbilityInfoId = RogueSaveGame->RogueDataInfos[RogueDataIndex].PreviewAbilityInfos[PawnClassId].NormalAttack; break;
			case ERogueAbilitySlot::Ground1: Res.AbilityInfoId = RogueSaveGame->RogueDataInfos[RogueDataIndex].PreviewAbilityInfos[PawnClassId].Ground1; break;
			case ERogueAbilitySlot::Ground2: Res.AbilityInfoId = RogueSaveGame->RogueDataInfos[RogueDataIndex].PreviewAbilityInfos[PawnClassId].Ground2; break;
			case ERogueAbilitySlot::Air1: Res.AbilityInfoId = RogueSaveGame->RogueDataInfos[RogueDataIndex].PreviewAbilityInfos[PawnClassId].Air1; break;
			case ERogueAbilitySlot::Air2: Res.AbilityInfoId = RogueSaveGame->RogueDataInfos[RogueDataIndex].PreviewAbilityInfos[PawnClassId].Air2; break;
			default: ;
			}
		}
	}
	return Res;
}

void UAwRogueDataSystem::SetPreviewAbilityLevelInfosByClassId(FString PawnClassId,
	TMap<ERogueAbilitySlot, FRougeAbilityLevelInfo> AbilityLevelInfos)
{
	if (HasCurRogueData())
	{
		if (RogueSaveGame->RogueDataInfos[RogueDataIndex].PreviewAbilityInfos.Contains(PawnClassId))
		{
			if(AbilityLevelInfos.Contains(ERogueAbilitySlot::NormalAttack))
				RogueSaveGame->RogueDataInfos[RogueDataIndex].PreviewAbilityInfos[PawnClassId].NormalAttack =
					AbilityLevelInfos[ERogueAbilitySlot::NormalAttack].AbilityInfoId;
			if(AbilityLevelInfos.Contains(ERogueAbilitySlot::Ground1))
				RogueSaveGame->RogueDataInfos[RogueDataIndex].PreviewAbilityInfos[PawnClassId].Ground1 =
					AbilityLevelInfos[ERogueAbilitySlot::Ground1].AbilityInfoId;
			if(AbilityLevelInfos.Contains(ERogueAbilitySlot::Ground2))
				RogueSaveGame->RogueDataInfos[RogueDataIndex].PreviewAbilityInfos[PawnClassId].Ground2 =
					AbilityLevelInfos[ERogueAbilitySlot::Ground2].AbilityInfoId;
			if(AbilityLevelInfos.Contains(ERogueAbilitySlot::Air1))
				RogueSaveGame->RogueDataInfos[RogueDataIndex].PreviewAbilityInfos[PawnClassId].Air1 =
					AbilityLevelInfos[ERogueAbilitySlot::Air1].AbilityInfoId;
			if(AbilityLevelInfos.Contains(ERogueAbilitySlot::Air2))
				RogueSaveGame->RogueDataInfos[RogueDataIndex].PreviewAbilityInfos[PawnClassId].Air2 =
					AbilityLevelInfos[ERogueAbilitySlot::Air2].AbilityInfoId;
		}
		else
		{
			FBattleAbilityIds BattleAbilityIds = FBattleAbilityIds();
			if(AbilityLevelInfos.Contains(ERogueAbilitySlot::NormalAttack))
				BattleAbilityIds.NormalAttack = AbilityLevelInfos[ERogueAbilitySlot::NormalAttack].AbilityInfoId;
			if(AbilityLevelInfos.Contains(ERogueAbilitySlot::Ground1))
				BattleAbilityIds.Ground1 = AbilityLevelInfos[ERogueAbilitySlot::Ground1].AbilityInfoId;
			if(AbilityLevelInfos.Contains(ERogueAbilitySlot::Ground2))
				BattleAbilityIds.Ground2 = AbilityLevelInfos[ERogueAbilitySlot::Ground2].AbilityInfoId;
			if(AbilityLevelInfos.Contains(ERogueAbilitySlot::Air1))
				BattleAbilityIds.Air1 = AbilityLevelInfos[ERogueAbilitySlot::Air1].AbilityInfoId;
			if(AbilityLevelInfos.Contains(ERogueAbilitySlot::Air2))
				BattleAbilityIds.Air2 = AbilityLevelInfos[ERogueAbilitySlot::Air2].AbilityInfoId;
			RogueSaveGame->RogueDataInfos[RogueDataIndex].PreviewAbilityInfos.Add(PawnClassId,BattleAbilityIds);
		}
	}
}

void UAwRogueDataSystem::SetPreviewAbilityLevelInfos(const int PlayerIndex,TMap<ERogueAbilitySlot, FRougeAbilityLevelInfo> AbilityLevelInfos)
{
	const FString PawnClassId = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>()->GetCurPawnClassId(PlayerIndex);
	if (HasCurRogueData())
	{
		if (RogueSaveGame->RogueDataInfos[RogueDataIndex].PreviewAbilityInfos.Contains(PawnClassId))
		{
			if(AbilityLevelInfos.Contains(ERogueAbilitySlot::NormalAttack))
				RogueSaveGame->RogueDataInfos[RogueDataIndex].PreviewAbilityInfos[PawnClassId].NormalAttack =
					AbilityLevelInfos[ERogueAbilitySlot::NormalAttack].AbilityInfoId;
			if(AbilityLevelInfos.Contains(ERogueAbilitySlot::Ground1))
				RogueSaveGame->RogueDataInfos[RogueDataIndex].PreviewAbilityInfos[PawnClassId].Ground1 =
					AbilityLevelInfos[ERogueAbilitySlot::Ground1].AbilityInfoId;
			if(AbilityLevelInfos.Contains(ERogueAbilitySlot::Ground2))
				RogueSaveGame->RogueDataInfos[RogueDataIndex].PreviewAbilityInfos[PawnClassId].Ground2 =
					AbilityLevelInfos[ERogueAbilitySlot::Ground2].AbilityInfoId;
			if(AbilityLevelInfos.Contains(ERogueAbilitySlot::Air1))
				RogueSaveGame->RogueDataInfos[RogueDataIndex].PreviewAbilityInfos[PawnClassId].Air1 =
					AbilityLevelInfos[ERogueAbilitySlot::Air1].AbilityInfoId;
			if(AbilityLevelInfos.Contains(ERogueAbilitySlot::Air2))
				RogueSaveGame->RogueDataInfos[RogueDataIndex].PreviewAbilityInfos[PawnClassId].Air2 =
					AbilityLevelInfos[ERogueAbilitySlot::Air2].AbilityInfoId;
		}
		else
		{
			FBattleAbilityIds BattleAbilityIds = FBattleAbilityIds();
			if(AbilityLevelInfos.Contains(ERogueAbilitySlot::NormalAttack))
				BattleAbilityIds.NormalAttack = AbilityLevelInfos[ERogueAbilitySlot::NormalAttack].AbilityInfoId;
			if(AbilityLevelInfos.Contains(ERogueAbilitySlot::Ground1))
				BattleAbilityIds.Ground1 = AbilityLevelInfos[ERogueAbilitySlot::Ground1].AbilityInfoId;
			if(AbilityLevelInfos.Contains(ERogueAbilitySlot::Ground2))
				BattleAbilityIds.Ground2 = AbilityLevelInfos[ERogueAbilitySlot::Ground2].AbilityInfoId;
			if(AbilityLevelInfos.Contains(ERogueAbilitySlot::Air1))
				BattleAbilityIds.Air1 = AbilityLevelInfos[ERogueAbilitySlot::Air1].AbilityInfoId;
			if(AbilityLevelInfos.Contains(ERogueAbilitySlot::Air2))
				BattleAbilityIds.Air2 = AbilityLevelInfos[ERogueAbilitySlot::Air2].AbilityInfoId;
			RogueSaveGame->RogueDataInfos[RogueDataIndex].PreviewAbilityInfos.Add(PawnClassId,BattleAbilityIds);
		}
	}
}

void UAwRogueDataSystem::SetPreviewAbilityLevelInfo(const int PlayerIndex,ERogueAbilitySlot AbilitySlot,
	FRougeAbilityLevelInfo AbilityLevelInfo)
{
	const FString PawnClassId = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>()->GetCurPawnClassId(PlayerIndex);
	if (HasCurRogueData())
	{
		if (RogueSaveGame->RogueDataInfos[RogueDataIndex].PreviewAbilityInfos.Contains(PawnClassId))
		{
			switch (AbilitySlot)
			{
			case ERogueAbilitySlot::None: break;
			case ERogueAbilitySlot::NormalAttack:
				RogueSaveGame->RogueDataInfos[RogueDataIndex].PreviewAbilityInfos[PawnClassId].NormalAttack = AbilityLevelInfo.AbilityInfoId;
				break;
			case ERogueAbilitySlot::Ground1:
				RogueSaveGame->RogueDataInfos[RogueDataIndex].PreviewAbilityInfos[PawnClassId].Ground1 = AbilityLevelInfo.AbilityInfoId;
				break;
			case ERogueAbilitySlot::Ground2:
				RogueSaveGame->RogueDataInfos[RogueDataIndex].PreviewAbilityInfos[PawnClassId].Ground2 = AbilityLevelInfo.AbilityInfoId;
				break;
			case ERogueAbilitySlot::Air1:
				RogueSaveGame->RogueDataInfos[RogueDataIndex].PreviewAbilityInfos[PawnClassId].Air1 = AbilityLevelInfo.AbilityInfoId;
				break;
			case ERogueAbilitySlot::Air2: 
				RogueSaveGame->RogueDataInfos[RogueDataIndex].PreviewAbilityInfos[PawnClassId].Air2 = AbilityLevelInfo.AbilityInfoId;
				break;
			}
		}
		else
		{
			FBattleAbilityIds BattleAbilityIds = FBattleAbilityIds();
			switch (AbilitySlot)
			{
			case ERogueAbilitySlot::None: break;
			case ERogueAbilitySlot::NormalAttack:
				BattleAbilityIds.NormalAttack = AbilityLevelInfo.AbilityInfoId;
				break;
			case ERogueAbilitySlot::Ground1:
				BattleAbilityIds.Ground1 = AbilityLevelInfo.AbilityInfoId;
				break;
			case ERogueAbilitySlot::Ground2:
				BattleAbilityIds.Ground2 = AbilityLevelInfo.AbilityInfoId;
				break;
			case ERogueAbilitySlot::Air1:
				BattleAbilityIds.Air1 = AbilityLevelInfo.AbilityInfoId;
				break;
			case ERogueAbilitySlot::Air2: 
				BattleAbilityIds.Air2 = AbilityLevelInfo.AbilityInfoId;
				break;
			}
			RogueSaveGame->RogueDataInfos[RogueDataIndex].PreviewAbilityInfos.Add(PawnClassId,BattleAbilityIds);
		}
	}
}

FUnlockAbilityInfo UAwRogueDataSystem::GetUnlockAbilityInfo(const FString& ClassId) const
{
	if (HasCurRogueData())
	{
		if (RogueSaveGame->RogueDataInfos[RogueDataIndex].UnlockAbilityId.Contains(ClassId))
			return RogueSaveGame->RogueDataInfos[RogueDataIndex].UnlockAbilityId[ClassId];
	}
	return FUnlockAbilityInfo();
}

void UAwRogueDataSystem::AddUnlockAbilityId(FString ClassId, FString AbilityId)
{
	if (HasCurRogueData())
	{
		if (!RogueSaveGame->RogueDataInfos[RogueDataIndex].UnlockAbilityId.Contains(ClassId))
			RogueSaveGame->RogueDataInfos[RogueDataIndex].UnlockAbilityId.Add(ClassId, FUnlockAbilityInfo());
		RogueSaveGame->RogueDataInfos[RogueDataIndex].UnlockAbilityId[ClassId].UnlockAbilityIds.Add(AbilityId);
	}
}

bool UAwRogueDataSystem::IsAbilityUnlock(const FString& ClassId, const FString& AbilityId) const
{
	if (HasCurRogueData())
	{
		if (RogueSaveGame->RogueDataInfos[RogueDataIndex].UnlockAbilityId.Contains(ClassId))
			return RogueSaveGame->RogueDataInfos[RogueDataIndex].UnlockAbilityId[ClassId].UnlockAbilityIds.Contains(AbilityId);
		else
			return false;
	}
	return false;
}

int UAwRogueDataSystem::GetRelicHasGetRecord(FString RelicId)
{
	if (HasCurRogueData())
	{
		if (RogueSaveGame->RogueDataInfos[RogueDataIndex].HasGotRelic.Contains(RelicId))
			return RogueSaveGame->RogueDataInfos[RogueDataIndex].HasGotRelic[RelicId];
	}
	return 0;
}

void UAwRogueDataSystem::SetRelicHasGetRecord(FString RelicId)
{
	if (HasCurRogueData())
	{
		RogueSaveGame->RogueDataInfos[RogueDataIndex].HasGotRelic.Add(RelicId, 1);
	}
}

int UAwRogueDataSystem::GetMagicItemHasGetRecord(FString ItemId)
{
	if (HasCurRogueData())
	{
		if (RogueSaveGame->RogueDataInfos[RogueDataIndex].HasGotMagicItem.Contains(ItemId))
			return RogueSaveGame->RogueDataInfos[RogueDataIndex].HasGotMagicItem[ItemId];
	}
	return 0;
}

void UAwRogueDataSystem::SetMagicItemHasGetRecord(FString ItemId)
{
	if (HasCurRogueData())
	{
		RogueSaveGame->RogueDataInfos[RogueDataIndex].HasGotMagicItem.Add(ItemId, 1);
	}
}

bool UAwRogueDataSystem::GetHasUnlockClass(const FString& ClassId) const
{
	if (HasCurRogueData())
		return RogueSaveGame->RogueDataInfos[RogueDataIndex].UnLockClassId.Contains(ClassId);
	return false;
}

void UAwRogueDataSystem::SetClassUnlock(FString ClassId)
{
	if (HasCurRogueData())
	{
		if(!GetHasUnlockClass(ClassId))
			RogueSaveGame->RogueDataInfos[RogueDataIndex].UnLockClassId.Add(ClassId);
		if(!RogueSaveGame->RogueDataInfos[RogueDataIndex].PreviewAbilityInfos.Contains(ClassId))
		{
			URogueBattleUpgradeSubSystem* BattleUpgradeSubSystem = GetGameInstance()->GetSubsystem<URogueBattleUpgradeSubSystem>();
			for (auto pc : UGameplayFuncLib::GetAllLocalAwPlayerControllers())
			{
				if (!pc)continue;
				if(pc->CurCharacter->PlayerClassId == ClassId)
				{
					int playerIndex = pc->GetLocalPCIndex();
					BattleUpgradeSubSystem->SettPreviewAbilityInfos(playerIndex,GetDefaultAbilityLevelInfos(ClassId), true);
					BattleUpgradeSubSystem->SetAbilityInfos(playerIndex,GetDefaultAbilityLevelInfos(ClassId), false);
				}
			}
		}
	}
}

FString UAwRogueDataSystem::GetCurrWeaponId(const FString& ClassId) const
{
	if (HasCurRogueData())
	{
		if (RogueSaveGame->RogueDataInfos[RogueDataIndex].CurrWeapon.Contains(ClassId))
			return RogueSaveGame->RogueDataInfos[RogueDataIndex].CurrWeapon[ClassId];
		else
		{
			UAwDataManager* DataManager = UGameplayFuncLib::GetAwDataManager();
			if (DataManager->GetWeaponDefaultInfo_CurrWeapon().Contains(ClassId))
				return DataManager->GetWeaponDefaultInfo_CurrWeapon()[ClassId];
		}
	}
	return "";
}

void UAwRogueDataSystem::SetCurrWeaponId(const FString ClassId, FString WeaponId) const
{
	if (HasCurRogueData())
	{
		if (RogueSaveGame->RogueDataInfos[RogueDataIndex].CurrWeapon.Contains(ClassId))
			RogueSaveGame->RogueDataInfos[RogueDataIndex].CurrWeapon[ClassId] = WeaponId;
		else
			RogueSaveGame->RogueDataInfos[RogueDataIndex].CurrWeapon.Add(ClassId, WeaponId);
	}
}

bool UAwRogueDataSystem::CheckWeaponUnlock(const FString& WeaponId) const
{
	if (HasCurRogueData())
	{
		if (RogueSaveGame->RogueDataInfos[RogueDataIndex].UnlockWeapons.Contains(WeaponId))
			return true;
		else
		{
			UAwDataManager* DataManager = UGameplayFuncLib::GetAwDataManager();
			if (DataManager->GetWeaponDefaultInfo_UnlockWeapon().Contains(WeaponId))
				return true;
		}
	}
	return false;
}

bool UAwRogueDataSystem::CheckSkinUnlock(const int SkinId) const
{
	if (HasCurRogueData())
	{
		auto unlocks = RogueSaveGame->RogueDataInfos[RogueDataIndex].UnlockSkins;
		if (unlocks.Contains(SkinId))
			return unlocks.Contains(SkinId);
		else
		{
			if (SkinId % 100 == 0)
			{
				SetSkinUnlock(SkinId);
				return true;
			}
		}
	}
	return false;
}

void UAwRogueDataSystem::SetSkinUnlock(const int SkinId) const
{
	if (HasCurRogueData())
	{
		if (RogueSaveGame->RogueDataInfos[RogueDataIndex].UnlockSkins.Contains(SkinId))
			RogueSaveGame->RogueDataInfos[RogueDataIndex].UnlockSkins[SkinId] = true;
		else
			RogueSaveGame->RogueDataInfos[RogueDataIndex].UnlockSkins.Add(SkinId,true);
	}
}

void UAwRogueDataSystem::SetWeaponUnlock(const FString WeaponId) const
{
	if (HasCurRogueData())
	{
		if (!RogueSaveGame->RogueDataInfos[RogueDataIndex].UnlockWeapons.Contains(WeaponId))
			RogueSaveGame->RogueDataInfos[RogueDataIndex].UnlockWeapons.Add(WeaponId, 0);
	}
}

int UAwRogueDataSystem::GetWeaponLevel(const FString& WeaponId) const
{
	if (HasCurRogueData())
	{
		if (RogueSaveGame->RogueDataInfos[RogueDataIndex].UnlockWeapons.Contains(WeaponId))
			return RogueSaveGame->RogueDataInfos[RogueDataIndex].UnlockWeapons[WeaponId];
		else
		{
			UAwDataManager* DataManager = UGameplayFuncLib::GetAwDataManager();
			if (DataManager->GetWeaponDefaultInfo_UnlockWeapon().Contains(WeaponId))
				return DataManager->GetWeaponDefaultInfo_UnlockWeapon()[WeaponId];
		}
	}
	return -1;
}

int UAwRogueDataSystem::SetWeaponLevel(FString WeaponId, int NewLevel)
{
	if (HasCurRogueData())
	{
		if (RogueSaveGame->RogueDataInfos[RogueDataIndex].UnlockWeapons.Contains(WeaponId))
		{
			const FRogueWeaponInfo Info = UGameplayFuncLib::GetDataManager()->GetRogueWeaponInfo(WeaponId);
			const int MaxLevel = Info.Cost.Num() - 1;
			if (NewLevel > MaxLevel)
				return MaxLevel;
			RogueSaveGame->RogueDataInfos[RogueDataIndex].UnlockWeapons[WeaponId] = NewLevel;
			return NewLevel;
		}
		else
		{
			RogueSaveGame->RogueDataInfos[RogueDataIndex].UnlockWeapons.Add(WeaponId, NewLevel);
			return NewLevel;
		}
	}
	return -1;
}

void UAwRogueDataSystem::AddItemInsts(TArray<FItemInstance> Items)
{
	RogueSaveGame->RogueDataInfos[RogueDataIndex].InventoryItemInstances.Append(Items);
	SaveData();
}

TArray<FItemInstance> UAwRogueDataSystem::GetInventoryItems() const
{
	return RogueSaveGame->RogueDataInfos[RogueDataIndex].InventoryItemInstances;
}

TArray<FItemInstance> UAwRogueDataSystem::GetEquippedItems() const
{
	return RogueSaveGame->RogueDataInfos[RogueDataIndex].EquippedItemInstances;
}

void UAwRogueDataSystem::ResetOldSaveGame(UAwRogueSaveGame* OldSave, UAwRogueSaveGame* NewSave)
{
	if (IsValid(OldSave)&&IsValid(NewSave))
	{
		if(OldSave->RogueDataInfos.Num()>0&&NewSave->RogueDataInfos.Num()>0)
		{
			int Soul = 0;
			int Shard = 0;
			if (OldSave->RogueDataInfos[0].Currency.Contains("Rogue_Soul"))
			{
				 Soul += OldSave->RogueDataInfos[0].Currency["Rogue_Soul"];
			}
			if (OldSave->RogueDataInfos[0].Currency.Contains("Rogue_Shard"))
			{
				Shard += OldSave->RogueDataInfos[0].Currency["Rogue_Shard"];
			}
			for (auto Talent:OldSave->RogueDataInfos[0].UnlockTalent)
			{
				Soul+= Talent.Value*150;
			}
			for (auto  Weapon : OldSave->RogueDataInfos[0].UnlockWeapons)
			{
				Shard+=Weapon.Value*3;
			}
			if (NewSave->RogueDataInfos[0].Currency.Contains("Rogue_Soul"))
			{
				Soul += NewSave->RogueDataInfos[0].Currency["Rogue_Soul"];
			}
			
			if (NewSave->RogueDataInfos[0].Currency.Contains("Rogue_Shard"))
			{
				Shard += NewSave->RogueDataInfos[0].Currency["Rogue_Shard"];
			}
			
			NewSave->RogueDataInfos[0].Currency.Add("Rogue_Soul",Soul);
			NewSave->RogueDataInfos[0].Currency.Add("Rogue_Shard",Shard);

			OldSave->RogueDataInfos.RemoveAt(0);
			UGameplayStatics::SaveGameToSlot(OldSave, "AwRogueGameSaveData", 0);
		}
	}
	else
	{
		return;
	}
}

