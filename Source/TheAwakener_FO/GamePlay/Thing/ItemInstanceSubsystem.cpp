// The copyright of this project belongs to Shanghai Minghuan Software Technology Co., Ltd


#include "ItemInstanceSubsystem.h"

#include "MathUtil.h"
#include "TheAwakener_FO/FunctionLibrary/CommonFuncLib.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GamePlay/Buff/BuffManager.h"

void UItemInstanceSubsystem::ReGetItemInstanceEquipped()
{
	//移除实际效果Buff
	for (auto PC : UGameplayFuncLib::GetAllLocalAwPlayerControllers())
	{
		if (!PC || !PC->CurCharacter)
		{
			return;
		}
		PC->CurCharacter->RemoveBuffByTag("ItemInstance");
	}
		UAwRogueDataSystem* DataSystem = GetGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	if (DataSystem&&DataSystem->HasCurRogueData())
	{
		bool HasData = false;
		FRogueDataInfo& Roguedata = DataSystem->GetCurRogueData(HasData);
		if (HasData)
		{
			for (int slot = 0; slot < Roguedata.EquippedItemInstances.Num(); slot++)
			{
				auto item = Roguedata.EquippedItemInstances[slot];
				for (auto PC : UGameplayFuncLib::GetAllLocalAwPlayerControllers())
				{
					EquipPlayerItem(item,PC,slot);
				}
			}
		}
	}
}

void UItemInstanceSubsystem::GivePlayerItem(FItemInstance Item, AAwPlayerController& PC)
{
	UAwRogueDataSystem* DataSystem = GetGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	if (DataSystem&&DataSystem->HasCurRogueData())
	{
		bool HasData = false;
		auto Roguedata = DataSystem->GetCurRogueData(HasData);
		if (HasData)
		{
			Roguedata.InventoryItemInstances.Add(Item);
		}
	}
}

void UItemInstanceSubsystem::RemoveItemEffect(FItemInstance Item, const AAwPlayerController* PC)
{

	UBuffManager::ProcessBuffs(Item.GetBuffStrings(),PC->CurCharacter,"ItemInst",false);
}

void UItemInstanceSubsystem::AddItemEffect(FItemInstance Item, const AAwPlayerController* PC)
{
	// Item.RandomAffixes
	UBuffManager::ProcessBuffs(Item.GetBuffStrings(),PC->CurCharacter,"ItemInst",true);
}

void UItemInstanceSubsystem::EquipPlayerItem(FItemInstance Item, AAwPlayerController* PC, int Slot)
{
	UAwRogueDataSystem* DataSystem = GetGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	if (DataSystem&&DataSystem->HasCurRogueData())
	{
		bool HasData = false;
		FRogueDataInfo& Roguedata = DataSystem->GetCurRogueData(HasData);
		if (HasData)
		{
			if (Roguedata.EquippedItemInstances.Num()<4)
			{
				Roguedata.EquippedItemInstances.SetNum(4);
			}
			if(!Roguedata.EquippedItemInstances[Slot].Model.IsEmpty())
			{
				auto OldItem = Roguedata.EquippedItemInstances[Slot];
				RemoveItemEffect(OldItem,PC);
				AddItemEffect(Item,PC);
			}
			else
			{
				AddItemEffect(Item,PC);
			}
			Roguedata.EquippedItemInstances[Slot] = Item;
		}
	}
}

void UItemInstanceSubsystem::UnarmPlayerItem(AAwPlayerController* PC, int Slot)
{
	UAwRogueDataSystem* DataSystem = GetGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	if (DataSystem&&DataSystem->HasCurRogueData())
	{
		bool HasData = false;
		FRogueDataInfo& Roguedata = DataSystem->GetCurRogueData(HasData);
		Roguedata.EquippedItemInstances[Slot] = FItemInstance();
	}
}

EItemRarity UItemInstanceSubsystem::GenerateRarityByMF(float MagicFoundRate)
{
	auto DataManager = UGameplayFuncLib::GetAwDataManager();
	if (!DataManager)
	{
		return EItemRarity::Normal;
	}
	return UCommonFuncLib::WeightedRandomSelectionFromMap<FItemRarityMultiplier>(DataManager->ItemRarityWeights,[&](const FItemRarityMultiplier& Data)->float
	{
		return Data.WeightByMf(MagicFoundRate);
	});
}

FItemAffix UItemInstanceSubsystem::GenerateAffixByMF(TArray<FString> AlreadyGetAffixGroup,float MagicFoundRate)
{
	FItemAffix Res;
	auto DataManager = UGameplayFuncLib::GetAwDataManager();

	if (DataManager)
	{
		// 构建过滤后的词条池，剔除与AlreadyGetAffixGroup重叠的词条
		TMap<FString, FItemAffixModel> FilteredAffixes;
		for (const auto& AffixPair : DataManager->ItemInstanceAffixes)
		{
			const FString& AffixId = AffixPair.Key;
			const FItemAffixModel& AffixModel = AffixPair.Value;

			// 如果该词条的组ID不在已获得的组列表中，则加入过滤后的池子
			if (!AlreadyGetAffixGroup.Contains(AffixModel.AffixGroup))
			{
				FilteredAffixes.Add(AffixId, AffixModel);
			}
		}

		// 从过滤后的池子中进行权重抽取
		FString randomKey = UCommonFuncLib::WeightedRandomSelectionFromMap<FItemAffixModel>(FilteredAffixes,[&](const FItemAffixModel& Data)->float
		{return Data.Weight * DataManager->GetRandWeightByMF(Data.Rarity,MagicFoundRate);});

		if (FilteredAffixes.Contains(randomKey))
		{
			auto affix = FilteredAffixes[randomKey];
			Res.AffixId = randomKey;
			Res.Value = FMath::RandRange(affix.ValueMin,affix.ValueMax);
		}
	}
	return Res;
}

FItemInstance UItemInstanceSubsystem::GenerateEquippedItemInstance(float MagicFoundRate, EThingType Type)
{
	FItemInstance ItemInstance;
	auto DataManager = UGameplayFuncLib::GetAwDataManager();
	if (DataManager)
	{
		FString randomKey = UCommonFuncLib::WeightedRandomSelectionFromMap<FItemInstanceModel>(DataManager->ItemInstanceModels,
			[&](const FItemInstanceModel& Data)->float{return Data.Type == Type ? 1:0;});
		ItemInstance.Model = randomKey;
		ItemInstance.ItemInstanceID = UAwGameInstance::GetSubsystem<UAwRogueDataSystem>()->GetNewItemInstanceID();
		EItemRarity Rarity = GenerateRarityByMF(MagicFoundRate);
		int AffixNum = FMath::RandRange(DataManager->ItemRarityWeights[Rarity].AffixNumMin,DataManager->ItemRarityWeights[Rarity].AffixNumMax);
		TArray<FString> AlreadyGetAffix;
		for (int i = 0; i < AffixNum; ++i)
		{
			auto NewAffix = GenerateAffixByMF(AlreadyGetAffix,MagicFoundRate);
			auto AffixModel = DataManager->GetAffixModel(NewAffix.AffixId);
			if (!NewAffix.AffixId.IsEmpty())
			{
				ItemInstance.RandomAffixes.Add(NewAffix);
				AlreadyGetAffix.Add(AffixModel.AffixGroup);
			}
		}
	}
	return ItemInstance;
}

TArray<FItemInstance> UItemInstanceSubsystem::GetInventoryItems()
{
	auto DataSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	return DataSystem->GetInventoryItems();
}

TArray<FItemInstance> UItemInstanceSubsystem::GetEquippedItems()
{
	auto DataSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	return DataSystem->GetEquippedItems();
}

void UItemInstanceSubsystem::AddItemsToSave(TArray<FItemInstance> NewItems)
{
	auto DataSystem = UGameplayFuncLib::GetAwGameInstance()->GetSubsystem<UAwRogueDataSystem>();
	DataSystem->AddItemInsts(NewItems);
}

float UItemInstanceSubsystem::GetPlayerIIR()
{
	return 0;
}

void UItemInstanceSubsystem::ClearDropItems()
{
	DropItems.Empty();
	if (OnPlayerGetLootBox.IsBound())
	{
		OnPlayerLoseLootBox.Broadcast();
	}
}

void UItemInstanceSubsystem::AddDropItem(FString MobId, FString Alterid, float IIR)
{
	DropItems.Add(FDropItemInstanceBox{FStringPool::Register(MobId+Alterid),IIR});
	if (OnPlayerGetLootBox.IsBound())
	{
		OnPlayerGetLootBox.Broadcast();
	}
}
