// The copyright of this project belongs to Shanghai Minghuan Software Technology Co., Ltd

#pragma once

#include "CoreMinimal.h"
#include "ItemInstance.h"
#include "ThingObj.h"
#include "Subsystems/GameInstanceSubsystem.h"
#include "TheAwakener_FO/GameFramework/AwPlayerController.h"
#include "ItemInstanceSubsystem.generated.h"

DECLARE_DYNAMIC_MULTICAST_DELEGATE(FItemInstLootChangeDelegate);
/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UItemInstanceSubsystem : public UGameInstanceSubsystem
{
	GENERATED_BODY()
public:
	void ReGetItemInstanceEquipped();
	void GivePlayerItem(FItemInstance Item,AAwPlayerController& PC);
	static void RemoveItemEffect(FItemInstance Item, const AAwPlayerController* PC);
	UFUNCTION(BlueprintCallable)
	static void AddItemEffect(FItemInstance Item, const AAwPlayerController* PC);
	UFUNCTION(BlueprintCallable)
	void EquipPlayerItem(FItemInstance Item,AAwPlayerController* PC,int Slot);
	UFUNCTION(BlueprintCallable)
	void UnarmPlayerItem(AAwPlayerController* PC,int Slot);
	EItemRarity GenerateRarityByMF(float MagicFoundRate);
	FItemAffix GenerateAffixByMF(TArray<FString> AlreadyGetAffixGroup,float MagicFoundRate);
	//WeaponObj or Item, else ALL
	UFUNCTION(BlueprintCallable)
	FItemInstance GenerateEquippedItemInstance(float MagicFoundRate,EThingType Type);
	UFUNCTION(BlueprintCallable)
	TArray<FItemInstance> GetInventoryItems();
	UFUNCTION(BlueprintCallable)
	TArray<FItemInstance> GetEquippedItems();
	UFUNCTION(BlueprintCallable)
	void AddItemsToSave(TArray<FItemInstance> NewItems);
	UFUNCTION(BlueprintPure)
	float GetPlayerIIR();
	UPROPERTY(BlueprintReadOnly)
	TArray<FDropItemInstanceBox> DropItems;
	UFUNCTION(BlueprintCallable)
	void ClearDropItems();
	UFUNCTION(BlueprintCallable)
	void AddDropItem(FString MobId,FString Alterid,float IIR);
	
	UPROPERTY(BlueprintAssignable)
	FItemInstLootChangeDelegate OnPlayerGetLootBox;
	UPROPERTY(BlueprintAssignable)
	FItemInstLootChangeDelegate OnPlayerLoseLootBox;
};
